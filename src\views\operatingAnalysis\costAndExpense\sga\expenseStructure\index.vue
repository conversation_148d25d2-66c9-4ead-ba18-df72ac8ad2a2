<template>
  <div class="expense-structure">
    <div class="chart-container">
      <!-- 管理费用图表 -->
      <div class="chart-item">
        <div class="chart-title">管理费用</div>
        <div class="chart-box" ref="managementChart"></div>
      </div>
      
      <!-- 销售费用图表 -->
      <div class="chart-item">
        <div class="chart-title">销售费用</div>
        <div class="chart-box" ref="salesChart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "ExpenseStructure",
  data() {
    return {
      // 管理费用数据结构
      managementData: {
        categories: ["本年累计", "同期预算"],
        series: [
          {
            name: "人工费",
            data: [40, 35],
            color: "#FF6B6B"
          },
          {
            name: "会议费用",
            data: [15, 18],
            color: "#4ECDC4"
          },
          {
            name: "办公费",
            data: [20, 22],
            color: "#45B7D1"
          },
          {
            name: "招待费",
            data: [10, 12],
            color: "#96CEB4"
          },
          {
            name: "差旅费用",
            data: [15, 13],
            color: "#FFEAA7"
          }
        ]
      },
      
      // 销售费用数据结构
      salesData: {
        categories: ["本年累计", "同期预算"],
        series: [
          {
            name: "代理费",
            data: [25, 30],
            color: "#DDA0DD"
          },
          {
            name: "商检费",
            data: [15, 12],
            color: "#98D8C8"
          },
          {
            name: "管道运输费",
            data: [35, 38],
            color: "#F7DC6F"
          },
          {
            name: "提运费",
            data: [25, 20],
            color: "#FFB6C1"
          }
        ]
      },
      
      managementChartInstance: null,
      salesChartInstance: null
    };
  },
  
  mounted() {
    this.$nextTick(() => {
      this.initCharts();
    });
    
    // 监听窗口大小变化
    this.resizeHandler = () => {
      if (this.managementChartInstance) {
        this.managementChartInstance.resize();
      }
      if (this.salesChartInstance) {
        this.salesChartInstance.resize();
      }
    };
    window.addEventListener("resize", this.resizeHandler);
  },
  
  beforeDestroy() {
    // 清理图表实例
    if (this.managementChartInstance) {
      this.managementChartInstance.dispose();
    }
    if (this.salesChartInstance) {
      this.salesChartInstance.dispose();
    }
    
    // 移除事件监听器
    if (this.resizeHandler) {
      window.removeEventListener("resize", this.resizeHandler);
    }
  },
  
  methods: {
    initCharts() {
      this.initManagementChart();
      this.initSalesChart();
    },
    
    initManagementChart() {
      if (this.managementChartInstance) {
        this.managementChartInstance.dispose();
      }
      
      this.managementChartInstance = echarts.init(this.$refs.managementChart);
      
      const option = this.createChartOption(this.managementData);
      this.managementChartInstance.setOption(option);
    },
    
    initSalesChart() {
      if (this.salesChartInstance) {
        this.salesChartInstance.dispose();
      }
      
      this.salesChartInstance = echarts.init(this.$refs.salesChart);
      
      const option = this.createChartOption(this.salesData);
      this.salesChartInstance.setOption(option);
    },
    
    createChartOption(data) {
      return {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderColor: "rgba(172, 194, 226, 0.2)",
          borderWidth: 1,
          textStyle: {
            color: "#FEFEFF",
            fontSize: 12
          },
          formatter: (params) => {
            let result = `<div style="font-weight: bold; margin-bottom: 8px; color: #FEFEFF;">${params[0].axisValue}</div>`;
            let total = 0;

            params.forEach((param) => {
              total += param.value;
              result += `<div style="margin: 3px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 2px; margin-right: 8px;"></span>
                ${param.seriesName}: ${param.value}%
              </div>`;
            });

            result += `<div style="margin-top: 8px; padding-top: 5px; border-top: 1px solid rgba(172, 194, 226, 0.3); color: #FEFEFF; font-weight: bold;">
              总计: ${total}%
            </div>`;

            return result;
          }
        },
        
        legend: {
          data: data.series.map(item => item.name),
          top: 10,
          textStyle: {
            color: '#ffffff',
            fontSize: 11
          },
          icon: 'rect',
          itemWidth: 12,
          itemHeight: 8
        },

        grid: {
          left: '8%',
          right: '4%',
          bottom: '8%',
          top: '20%',
          containLabel: true
        },
        
        xAxis: {
          type: 'category',
          data: data.categories,
          axisLine: {
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.2)'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#ACC2E2',
              fontSize: 12
            },
            interval: 0,
            rotate: 0
          },
          axisTick: {
            show: false
          }
        },
        
        yAxis: {
          type: 'value',
          max: 100,
          name: '占比(%)',
          nameTextStyle: {
            color: '#ACC2E2',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.2)'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#ACC2E2',
              fontSize: 11
            },
            formatter: '{value}%'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.1)',
              type: 'dashed'
            }
          }
        },
        
        series: data.series.map((item, index) => ({
          name: item.name,
          type: 'bar',
          stack: 'total',
          data: item.data,
          itemStyle: {
            color: item.color,
            borderRadius: index === 0 ? [0, 0, 0, 0] : [0, 0, 0, 0]
          },
          barWidth: '60%'
        }))
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.expense-structure {
  width: 100%;
  height: 100%;
  padding: 10px;
  
  .chart-container {
    display: flex;
    height: 100%;
    gap: 20px;
    
    .chart-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
      
      .chart-title {
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        color: #FEFEFF;
        padding: 12px 0;
        letter-spacing: 0.5px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
      
      .chart-box {
        flex: 1;
        min-height: 300px;
        width: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .expense-structure {
    .chart-container {
      flex-direction: column;
      gap: 15px;
      
      .chart-item {
        .chart-box {
          min-height: 250px;
        }
      }
    }
  }
}
</style>
