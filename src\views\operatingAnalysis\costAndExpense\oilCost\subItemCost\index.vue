<template>
  <div class="sub-item-cost">
    <!-- 控制面板 -->
    <div class="control-panel">
      <el-select
        v-model="selectedDataGroup"
        placeholder="选择数据组"
        @change="onDataGroupChange"
        class="data-selector"
        size="small"
      >
        <el-option
          v-for="item in dataGroups"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>

      <div class="current-data-info">
        当前数据组: {{ getCurrentDataGroupLabel() }}
      </div>

      <div class="carousel-controls">
        <el-button
          @click="toggleCarousel"
          :type="isCarouselActive ? 'danger' : 'primary'"
          size="small"
          class="carousel-btn"
        >
          {{ isCarouselActive ? '停止轮播' : '开始轮播' }}
        </el-button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "SubItemCost",
  data() {
    return {
      myChart: null,
      option: {},

      // 固定的X轴标签（5个成本项目）
      xData: ["桶油OPEX", "桶油DD&A", "桶油其他税项", "桶油弃置费", "桶油SG&A"],

      // 轮播相关
      isCarouselActive: true,
      carouselTimer: null,
      carouselInterval: 5000, // 5秒切换一次
      selectedDataGroup: 'YC13-1',
      currentDataIndex: 0,

      // 数据组配置
      dataGroups: [
        { value: 'YC13-1', label: 'YC13-1' },
        { value: 'LS17-2', label: 'LS17-2' },
        { value: 'LS25-1', label: 'LS25-1' },
        { value: 'YC13-10', label: 'YC13-10' },
        { value: 'WC16-2', label: 'WC16-2' }
      ],

      // 五组数据的具体数值
      dataSource: {
        'YC13-1': {
          currentYear: [4.62, 11.75, 1.45, 0.84, 1.11],
          lastYear: [3.06, 10.41, 1.45, 0.81, 1.11]
        },
        'LS17-2': {
          currentYear: [5.23, 12.34, 1.67, 0.92, 1.25],
          lastYear: [3.45, 11.12, 1.52, 0.88, 1.18]
        },
        'LS25-1': {
          currentYear: [3.89, 10.98, 1.33, 0.76, 0.98],
          lastYear: [2.87, 9.87, 1.28, 0.73, 0.95]
        },
        'YC13-10': {
          currentYear: [6.12, 13.45, 1.89, 1.05, 1.42],
          lastYear: [4.23, 12.23, 1.76, 1.01, 1.35]
        },
        'WC16-2': {
          currentYear: [4.98, 11.23, 1.56, 0.89, 1.18],
          lastYear: [3.34, 10.67, 1.43, 0.85, 1.12]
        }
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      this.startCarousel();

      // 添加窗口大小变化监听
      this.resizeHandler = () => {
        if (this.myChart) {
          this.myChart.resize();
        }
      };
      window.addEventListener("resize", this.resizeHandler);
    });
  },
  beforeDestroy() {
    // 组件销毁时清理资源
    this.stopCarousel();
    if (this.myChart) {
      this.myChart.dispose();
    }
    // 清理窗口大小变化监听器
    if (this.resizeHandler) {
      window.removeEventListener("resize", this.resizeHandler);
    }
  },
  methods: {
    initChart() {
      // 如果已有实例，先销毁
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);

      // 获取当前选中数据组的数据
      const currentData = this.getCurrentData();
      const currentYearPageData = currentData.currentYear;
      const lastYearPageData = currentData.lastYear;

      this.option = {
        color: ["#248EFF", "#7262FD"], // 项目标准配色：本年累计(蓝色)、去年同期(紫色)
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText:
            "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(255, 255, 255, 0.1)",
            },
          },
          // 确保tooltip只显示有效的series数据
          showContent: true,
          alwaysShowContent: false,
          triggerOn: "mousemove",
          formatter: this.tooltipFormatter,
        },
        legend: {
          data: ["本年累计", "去年同期"],
          top: "3%", // 稍微增加顶部间距，与grid top协调
          right: "3%", // 与grid right保持一致
          orient: "horizontal", // 水平排列
          itemGap: 20, // 图例项之间的间距
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          itemWidth: 14, // 图例标记的宽度
          itemHeight: 10, // 图例标记的高度
        },
        grid: {
          top: "15%", // 保持顶部间距
          left: "5%", // 减少左侧间距，更好利用容器宽度
          right: "3%", // 减少右侧间距，向左移动图表
          bottom: "12%", // 减少底部间距，向下移动图表，仍保证X轴标签显示
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.xData, // 使用固定的5个成本项目
          axisTick: {
            alignWithLabel: true,
          },
          nameTextStyle: {
            color: "#ACC2E2",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 11, // 稍微增大字体以提高可读性
            },
            rotate: 45, // 旋转标签以适应长文本
            interval: 0, // 显示所有标签
            margin: 10, // 适当减少标签与轴线的间距，配合减少的bottom值
            overflow: "none", // 确保标签不被截断
          },
        },
        yAxis: {
          type: "value",
          name: "",
          position: "left",
          max: 16, // 根据图片设置最大值
          interval: 2, // 设置间隔
          nameTextStyle: {
            color: "#ACC2E2",
            align: "right",
            padding: [0, 15, 0, 0], // 增加右侧padding
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 12,
            },
            formatter: function (value) {
              return value.toFixed(2);
            },
            margin: 8, // 适当减少标签与轴线的间距，配合减少的left值
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.15)",
              type: "solid",
              width: 1,
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          // 本年累计 - 顶部装饰
          {
            name: "本年累计",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, -5],
            symbolPosition: "end",
            z: 12,
            label: {
              normal: {
                show: false,
              },
            },
            data: currentYearPageData,
          },
          // 本年累计 - 底部装饰
          {
            name: "本年累计",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [-12, 5],
            z: 12,
            data: currentYearPageData,
          },
          // 本年累计 - 主体柱子
          {
            name: "本年累计",
            type: "bar",
            data: currentYearPageData,
            barWidth: "20",
            itemStyle: {
              normal: {
                color: "#248EFF", // 本年累计使用项目标准蓝色
                opacity: 0.8,
              },
            },
            label: {
              show: false, // 去掉柱状图顶部的数字展示
            },
          },
          // 去年同期 - 顶部装饰
          {
            name: "去年同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, -5],
            symbolPosition: "end",
            z: 12,
            tooltip: {
              show: false,
            },
            label: {
              normal: {
                show: false,
              },
            },
            data: lastYearPageData,
          },
          // 去年同期 - 底部装饰
          {
            name: "去年同期",
            type: "pictorialBar",
            symbolSize: [20, 10],
            symbolOffset: [12, 5],
            z: 12,
            tooltip: {
              show: false, // 装饰性元素不显示tooltip
            },
            data: lastYearPageData,
          },
          // 去年同期 - 主体柱子
          {
            name: "去年同期",
            type: "bar",
            data: lastYearPageData,
            barWidth: "20",
            itemStyle: {
              normal: {
                color: "#7262FD", // 去年同期使用项目标准紫色
                opacity: 0.8,
              },
            },
            label: {
              show: false, // 去掉柱状图顶部的数字展示
            },
          },
        ],
      };

      this.myChart.setOption(this.option);

      // 添加鼠标事件监听，实现防抖优化
      this.addChartEventListeners();
    },



    // Tooltip格式化函数
    tooltipFormatter(params) {
      if (!params || params.length === 0) return "";

      const axisValue = params[0].axisValue;
      let tooltipContent = `<div style="margin-bottom: 8px; font-weight: bold; color: #FFFFFF;">${axisValue}</div>`;

      // 创建一个Map来存储唯一的系列数据，确保每个系列名称只显示一次
      const uniqueSeriesMap = new Map();

      // 遍历所有参数，只保留每个系列名称的第一个数据
      params.forEach((param) => {
        const seriesName = param.seriesName;
        const componentSubType = param.componentSubType;

        // 优先选择bar类型的数据，如果没有则选择第一个遇到的数据
        if (!uniqueSeriesMap.has(seriesName) || componentSubType === "bar") {
          uniqueSeriesMap.set(seriesName, {
            value: param.value,
            color: param.color,
            seriesName: param.seriesName,
            componentSubType: param.componentSubType,
          });
        }
      });

      // 按照预期的顺序显示数据：本年累计、去年同期
      const expectedOrder = ["本年累计", "去年同期"];

      expectedOrder.forEach((seriesName) => {
        if (uniqueSeriesMap.has(seriesName)) {
          const param = uniqueSeriesMap.get(seriesName);
          tooltipContent += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="margin-right: 8px; color: #FFFFFF;">${param.seriesName}:</span>
              <span style="font-weight: bold; color: #FFFFFF;">${param.value} $/boe</span>
            </div>
          `;
        }
      });

      return tooltipContent;
    },

    // 添加图表事件监听器，实现性能优化
    addChartEventListeners() {
      if (!this.myChart) return;

      // 鼠标移入事件 - 使用防抖优化
      this.myChart.on(
        "mouseover",
        this.debounce((params) => {
          // 可以在这里添加额外的鼠标悬停逻辑
          console.log("Chart mouseover:", params);
        }, 100)
      );

      // 鼠标移出事件
      this.myChart.on("mouseout", () => {
        // 清理防抖定时器
        if (this.tooltipTimer) {
          clearTimeout(this.tooltipTimer);
        }
      });
    },

    // 防抖函数，优化鼠标事件性能
    debounce(func, wait) {
      return (...args) => {
        if (this.tooltipTimer) {
          clearTimeout(this.tooltipTimer);
        }
        this.tooltipTimer = setTimeout(() => {
          func.apply(this, args);
        }, wait);
      };
    },

    // 获取当前数据组的数据
    getCurrentData() {
      return this.dataSource[this.selectedDataGroup] || this.dataSource['YC13-1'];
    },

    // 获取当前数据组的标签
    getCurrentDataGroupLabel() {
      const group = this.dataGroups.find(g => g.value === this.selectedDataGroup);
      return group ? group.label : 'YC13-1';
    },

    // 更新图表数据
    updateChart() {
      if (!this.myChart) return;

      const currentData = this.getCurrentData();
      const currentYearPageData = currentData.currentYear;
      const lastYearPageData = currentData.lastYear;

      // 更新图表配置中的数据
      this.option.series.forEach((series) => {
        if (series.name === '本年累计') {
          series.data = currentYearPageData;
        } else if (series.name === '去年同期') {
          series.data = lastYearPageData;
        }
      });

      // 应用新的配置，使用动画效果
      this.myChart.setOption(this.option, {
        notMerge: false,
        lazyUpdate: false,
        silent: false
      });
    },

    // 开始轮播
    startCarousel() {
      if (!this.isCarouselActive) return;

      this.stopCarousel(); // 先清理之前的定时器

      // 设置轮播定时器
      this.carouselTimer = setInterval(() => {
        this.nextDataGroup();
      }, this.carouselInterval);
    },

    // 停止轮播
    stopCarousel() {
      if (this.carouselTimer) {
        clearInterval(this.carouselTimer);
        this.carouselTimer = null;
      }
    },

    // 切换到下一个数据组
    nextDataGroup() {
      this.currentDataIndex = (this.currentDataIndex + 1) % this.dataGroups.length;
      this.selectedDataGroup = this.dataGroups[this.currentDataIndex].value;
      this.updateChart();
    },

    // 切换轮播状态
    toggleCarousel() {
      this.isCarouselActive = !this.isCarouselActive;

      if (this.isCarouselActive) {
        this.startCarousel();
      } else {
        this.stopCarousel();
      }
    },

    // 手动选择数据组
    onDataGroupChange(value) {
      // 停止轮播
      this.isCarouselActive = false;
      this.stopCarousel();

      // 更新当前索引
      this.currentDataIndex = this.dataGroups.findIndex(g => g.value === value);
      if (this.currentDataIndex === -1) {
        this.currentDataIndex = 0;
      }

      // 更新图表
      this.updateChart();
    },

  },
};
</script>

<style lang="scss" scoped>
.sub-item-cost {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  // 控制面板样式
  .control-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: rgba(26, 46, 94, 0.2);
    border-radius: 4px;
    margin-bottom: 8px;
    gap: 16px;

    .data-selector {
      width: 150px;
      flex-shrink: 0;

      ::v-deep .el-input__inner {
        background: rgba(26, 46, 94, 0.3);
        border: 1px solid rgba(172, 194, 226, 0.3);
        color: #acc2e2;

        &:focus {
          border-color: #248EFF;
        }
      }

      ::v-deep .el-input__suffix {
        color: #acc2e2;
      }

      ::v-deep .el-select-dropdown {
        background: rgba(12, 15, 41, 0.95);
        border: 1px solid rgba(172, 194, 226, 0.3);
      }

      ::v-deep .el-select-dropdown__item {
        color: #acc2e2;

        &:hover {
          background: rgba(36, 142, 255, 0.2);
        }

        &.selected {
          background: rgba(36, 142, 255, 0.3);
          color: #248EFF;
        }
      }
    }

    // 当前数据组信息样式（现在在控制面板内）
    .current-data-info {
      color: #248EFF;
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      flex: 1;
      text-align: center;
    }

    .carousel-controls {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .carousel-btn {
        min-width: 80px;
      }
    }
  }

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 280px;
    max-height: 350px;
  }
}
</style>
