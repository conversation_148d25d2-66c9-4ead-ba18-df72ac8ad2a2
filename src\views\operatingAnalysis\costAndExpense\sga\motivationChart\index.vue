<template>
  <div class="motivation-chart">
    <!-- 销售费用 - 同比增减动因 -->
    <div class="chart-container">
      <div class="chart-title">销售费用 - 同比增减动因</div>
      <div class="chart-box" ref="salesChart"></div>
    </div>
    
    <!-- 管理费用 - 同比增减动因 -->
    <div class="chart-container">
      <div class="chart-title">管理费用 - 同比增减动因</div>
      <div class="chart-box" ref="managementChart"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "MotivationChart",
  props: {
    // 可以通过props传入数据，支持动态更新
    salesChartData: {
      type: Array,
      default: () => []
    },
    managementChartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      salesChart: null,
      managementChart: null,
      // 销售费用瀑布图数据
      salesData: [
        { name: "去年同期", value: 4374, type: "start" },
        { name: "代理费", value: 218, type: "positive" },
        { name: "商检费", value: -6, type: "negative" },
        { name: "管道运输费", value: -614, type: "negative" },
        { name: "运输费", value: -423, type: "negative" },
        { name: "本年累计", value: 3113, type: "end" }
      ],
      // 管理费用瀑布图数据
      managementData: [
        { name: "去年同期", value: 235, type: "start" },
        { name: "人工成本", value: -64, type: "negative" },
        { name: "会议费用", value: -1, type: "negative" },
        { name: "办公费", value: -11, type: "negative" },
        { name: "招待费", value: -1, type: "negative" },
        { name: "差旅费", value: -4, type: "negative" },
        { name: "本年累计", value: 155, type: "end" }
      ]
    };
  },
  computed: {
    // 使用计算属性来合并默认数据和传入的props数据
    finalSalesData() {
      return this.salesChartData.length > 0 ? this.salesChartData : this.salesData;
    },
    finalManagementData() {
      return this.managementChartData.length > 0 ? this.managementChartData : this.managementData;
    }
  },
  watch: {
    // 监听数据变化，重新渲染图表
    finalSalesData: {
      handler() {
        this.$nextTick(() => {
          this.initSalesChart();
        });
      },
      deep: true
    },
    finalManagementData: {
      handler() {
        this.$nextTick(() => {
          this.initManagementChart();
        });
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initSalesChart();
      this.initManagementChart();
    });
  },
  beforeDestroy() {
    if (this.salesChart) {
      this.salesChart.dispose();
    }
    if (this.managementChart) {
      this.managementChart.dispose();
    }
  },
  methods: {
    initSalesChart() {
      if (this.salesChart) {
        this.salesChart.dispose();
      }

      this.salesChart = echarts.init(this.$refs.salesChart);
      const processedData = this.processWaterfallData(this.finalSalesData);
      const option = this.getChartOption(processedData, this.finalSalesData);
      this.salesChart.setOption(option);
    },

    initManagementChart() {
      if (this.managementChart) {
        this.managementChart.dispose();
      }

      this.managementChart = echarts.init(this.$refs.managementChart);
      const processedData = this.processWaterfallData(this.finalManagementData);
      const option = this.getChartOption(processedData, this.finalManagementData);
      this.managementChart.setOption(option);
    },

    // 处理瀑布图数据
    processWaterfallData(chartData) {
      const categories = [];
      const assistData = [];
      const displayData = [];
      let cumulative = 0;

      chartData.forEach((item, index) => {
        categories.push(item.name);

        if (item.type === "start") {
          // 起始值
          assistData.push(0);
          displayData.push(item.value);
          cumulative = item.value;
        } else if (item.type === "end") {
          // 结束值 - 计算最终累积值
          const finalValue = cumulative;
          assistData.push(0);
          displayData.push(finalValue);
          // 更新chartData中的最终值以保持一致性
          chartData[index].value = finalValue;
        } else {
          // 中间的增减值
          assistData.push(cumulative);
          displayData.push(item.value);
          cumulative += item.value;
        }
      });

      return {
        categories,
        assistData,
        displayData,
      };
    },

    // 获取图表配置选项
    getChartOption(processedData, originalData) {
      return {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: (params) => this.tooltipFormatter(params, originalData),
        },
        grid: {
          top: "10%",
          left: "8%",
          right: "8%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: processedData.categories,
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.3)",
            },
          },
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 11,
            interval: 0,
            rotate: 0,
            margin: 8,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "辅助",
            type: "bar",
            stack: "total",
            itemStyle: {
              color: "transparent",
            },
            emphasis: {
              itemStyle: {
                color: "transparent",
              },
            },
            data: processedData.assistData,
          },
          {
            name: "数值",
            type: "bar",
            stack: "total",
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 12,
              formatter: (params) => {
                const value = originalData[params.dataIndex].value;
                if (originalData[params.dataIndex].type === "positive") {
                  return `+${value}`;
                } else if (originalData[params.dataIndex].type === "negative") {
                  return `${value}`;
                }
                return value;
              },
            },
            itemStyle: {
              color: (params) => {
                const item = originalData[params.dataIndex];
                if (item.type === "start" || item.type === "end") {
                  return "#5B9BD5"; // 蓝色 - 与设计图一致
                } else if (item.type === "positive") {
                  return "#70AD47"; // 绿色 - 与设计图一致
                } else {
                  return "#FF8C00"; // 橙色 - 与设计图一致
                }
              },
            },
            data: processedData.displayData,
          },
        ],
      };
    },

    // Tooltip格式化函数
    tooltipFormatter(params, originalData) {
      if (!params || params.length === 0) return '';
      
      const dataIndex = params[0].dataIndex;
      const originalItem = originalData[dataIndex];
      
      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${originalItem.name}</div>`;
      
      if (originalItem.type === "start" || originalItem.type === "end") {
        content += `<div>数值: ${originalItem.value}</div>`;
      } else {
        const prefix = originalItem.type === "positive" ? "+" : "";
        content += `<div>变化: ${prefix}${originalItem.value}</div>`;
      }
      
      return content;
    },
  },
};
</script>

<style lang="scss" scoped>
.motivation-chart {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 10px;
  padding: 0;

  .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 0;

    .chart-title {
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #FEFEFF;
      padding: 12px 0;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .chart-box {
      flex: 1;
      min-height: 160px;
      width: 100%;
    }
  }
}
</style>
