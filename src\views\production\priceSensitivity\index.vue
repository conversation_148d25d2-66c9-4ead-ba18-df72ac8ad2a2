<template>
  <div class="price-sensitivity">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <h1>价格敏感性分析</h1>
        <div style="float: right; margin-top: -30px">
          选择纬度：
          <el-select v-model="modelType" placeholder="请选择">
            <el-option label="产销配比模型1" value="1"></el-option>
            <el-option label="产销配比模型2" value="2"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 基础参数设定 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>基础参数设定</h2>
        </div>
        <el-row :gutter="20">
          <el-form
            :model="form"
            :rules="formRules"
            ref="baseForm"
            :inline="true"
            label-position="right"
            label-width="126px"
            class="paramsForm"
          >
            <el-col :span="6" v-for="item in formItems" :key="item.label">
              <el-form-item :label="item.label" :prop="item.prop">
                <el-input
                  v-if="item.type === 'input'"
                  v-model="form[item.prop]"
                  placeholder="请输入"
                />
                <el-input
                  v-else-if="item.type === 'number'"
                  type="number"
                  v-model.number="form[item.prop]"
                  placeholder="请输入"
                />
                <el-select
                  v-else
                  v-model="form[item.prop]"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="option in item.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </el-card>

      <!-- 成本指标 -->
      <el-card shadow="hover" class="section-card card-left">
        <div slot="header">
          <h2>成本指标</h2>
        </div>
        <el-form :model="costForm" class="cost-form">
          <el-form-item label="完全成本">
            <el-input placeholder="请输入" v-model="costForm.fullCost">
              <el-select
                v-model="costForm.unit"
                slot="prepend"
                placeholder="单位"
              >
                <el-option label="￥" value="1" />
                <el-option label="$" value="2" />
              </el-select>
              <el-select
                v-model="costForm.unit"
                slot="append"
                placeholder="单位"
              >
                <el-option label="万元" value="1" />
                <el-option label="$" value="2" />
              </el-select>
            </el-input>
          </el-form-item>
          <el-form-item label="崖城13-1">
            <el-slider v-model="costForm.yaCheng13_1" show-input></el-slider>
          </el-form-item>
          <el-form-item label="崖城13-10">
            <el-slider v-model="costForm.yaCheng13_10" show-input></el-slider>
          </el-form-item>
          <el-form-item label="陵水17-2">
            <el-slider v-model="costForm.lingShui17_2" show-input></el-slider>
          </el-form-item>
          <el-form-item label="陵水25-1">
            <el-slider v-model="costForm.lingShui25_1" show-input></el-slider>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 约束条件配置 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>指标预览</h2>
        </div>

        <div class="section-box">
          <div class="card-left">
            <h3>价格预览</h3>
            <el-table :data="pricePreviewData" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="oilType"
                label="油气类型"
                width="180"
              ></el-table-column>
              <el-table-column
                prop="channel"
                label="渠道"
                width="180"
              ></el-table-column>
              <el-table-column label="价格" min-width="220">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.priceValue"
                    placeholder="请输入价格"
                    type="number"
                    size="small"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="card-right">
            <h3>销售预览</h3>
            <el-table :data="resultData" border style="width: 100%">
              <el-table-column
                prop="oilType"
                label="油气类型"
                min-width="120"
              />
              <el-table-column label="渠道" min-width="100">
                <template slot-scope="scope">
                  <span v-if="scope.row.oilType === '天然气'">{{
                    scope.row.channel
                  }}</span>
                  <span v-else></span>
                </template>
              </el-table-column>
              <el-table-column label="崖城13-1" min-width="120">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.yancheng13_1"
                    placeholder="请输入"
                    size="small"
                    type="number"
                  />
                </template>
              </el-table-column>
              <el-table-column label="崖城13-10" min-width="120">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.yancheng13_10"
                    placeholder="请输入"
                    size="small"
                    type="number"
                  />
                </template>
              </el-table-column>
              <el-table-column label="陵水17-2" min-width="120">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.lingshui17_2"
                    placeholder="请输入"
                    size="small"
                    type="number"
                  />
                </template>
              </el-table-column>
              <el-table-column label="陵水25-1" min-width="120">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.lingshui25_1"
                    placeholder="请输入"
                    size="small"
                    type="number"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <div class="action-buttons" v-if="!isCalculating">
        <el-button type="info" size="large" @click="loadTestData"
          >加载测试数据</el-button
        >
        <el-button type="success" size="large" @click="saveFormData"
          >保存参数</el-button
        >
        <el-button type="primary" size="large" @click="startCalculation"
          >开始测算</el-button
        >
      </div>

      <el-card shadow="hover" class="section-card" v-else>
        <div slot="header" class="card-header">
          <h2>测算结果</h2>
          <el-button type="primary" @click="resetCalculation">重置</el-button>
        </div>
        <div class="section-box">
          <div class="card-left">
            <div class="chart-box">
              <ProfitChart :chartData="apiResponseData"></ProfitChart>
            </div>
          </div>
          <div class="card-right">
            <h3>价格敏感性分析结果</h3>
            <div style="margin-bottom: 20px">
              <el-select
                v-model="selectedDevice"
                placeholder="请选择装置"
                style="width: 200px"
                @change="onDeviceChange"
              >
                <el-option
                  v-for="device in deviceOptions"
                  :key="device.name"
                  :label="device.name"
                  :value="device.name"
                />
              </el-select>
            </div>
            <el-table :data="currentDeviceData" border style="width: 100%">
              <el-table-column
                label="价格指数变化量"
                prop="brent"
              ></el-table-column>
              <el-table-column
                label="指数变化率(%)"
                prop="brentRatio"
              ></el-table-column>
              <el-table-column label="利润(万元)" prop="profit">
                <template slot-scope="scope">
                  {{
                    scope.row.profit !== null && scope.row.profit !== undefined
                      ? Number(scope.row.profit).toFixed(2)
                      : ""
                  }}
                </template>
              </el-table-column>
              <el-table-column label="利润影响(%)" prop="profitRatio">
                <template slot-scope="scope">
                  {{
                    scope.row.profitRatio !== null &&
                    scope.row.profitRatio !== undefined
                      ? Number(scope.row.profitRatio).toFixed(2)
                      : ""
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import ProfitChart from "./components/ProfitChart.vue";
import {
  priceSensitive,
  calculateGasPrice,
} from "@/api/production/priceSensitivity";

export default {
  name: "priceSensitivity",
  components: {
    ProfitChart,
  },
  data() {
    return {
      modelType: "",
      isCalculating: false,
      formItems: [
        // 基础参数
        {
          label: "Brent基准价格：",
          prop: "brentBenchmark",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "基础利润：",
          prop: "baseProfit",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "Brent下限：",
          prop: "brentLowerLimit",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "Brent上限：",
          prop: "brentUpperLimit",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "汇率：",
          prop: "exchangeRate",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "完全成本：",
          prop: "fullCost",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "最小变化量：",
          prop: "minChange",
          placeholder: "请输入",
          type: "number",
        },
        {
          label: "指标单位：",
          prop: "unit",
          placeholder: "请选择",
          type: "select",
          options: [
            { label: "百分比", value: 1 },
            { label: "数值", value: 2 },
          ],
        },
        {
          label: "Brent/JCC比率：",
          prop: "ratio",
          placeholder: "请输入",
          type: "number",
        },
      ],
      formRules: {
        brentBenchmark: [
          { required: true, message: "请输入Brent基准价格", trigger: "blur" },
          { type: "number", message: "请输入有效数字", trigger: "blur" },
        ],
        brentLowerLimit: [
          { required: true, message: "请输入Brent下限", trigger: "blur" },
          { type: "number", message: "请输入有效数字", trigger: "blur" },
        ],
        brentUpperLimit: [
          { required: true, message: "请输入Brent上限", trigger: "blur" },
          { type: "number", message: "请输入有效数字", trigger: "blur" },
        ],
        exchangeRate: [
          { required: true, message: "请输入汇率", trigger: "blur" },
          { type: "number", message: "请输入有效数字", trigger: "blur" },
        ],
        fullCost: [
          { required: true, message: "请输入完全成本", trigger: "blur" },
          { type: "number", message: "请输入有效数字", trigger: "blur" },
        ],
        minChange: [
          { required: true, message: "请输入最小变化量", trigger: "blur" },
          { type: "number", message: "请输入有效数字", trigger: "blur" },
        ],
        ratio: [
          { required: true, message: "请输入Brent/JCC比率", trigger: "blur" },
          { type: "number", message: "请输入有效数字", trigger: "blur" },
        ],
      },
      form: {
        // 基础参数
        baseProfit: null,
        brentBenchmark: null,
        brentLowerLimit: null,
        brentUpperLimit: null,
        exchangeRate: null,
        fullCost: null,
        minChange: null,
        ratio: null,
        unit: 1,

        // 广东地区天然气价格参数
        gd1310gas: null,
        gd131gas: null,
        gd172gas: null,
        gd251gas: null,

        // 香港地区天然气价格参数
        hk1310gas: null,
        hk131gas: null,
        hk172gas: null,
        hk251gas: null,

        // 海南地区天然气价格参数
        hn1310gas: null,
        hn131gas: null,
        hn172gas: null,
        hn251gas: null,

        // 凝析油参数
        ls172oil: null,
        ls251oil: null,
        yc1310oil: null,
        yc131oil: null,
      },
      costForm: {
        fullCost: "",
        unit: "1",
        yaCheng13_1: 0,
        yaCheng13_10: 0,
        lingShui17_2: 0,
        lingShui25_1: 0,
      },
      priceForm: {
        hkPrice: null, // 香港中电价格
        hnPrice: null, // 气电南山价格
        gdPrice: null, // 气电广东价格
      },
      pricePreviewData: [
        {
          oilType: "天然气",
          channel: "香港中电",
          priceValue: null,
          priceField: "hkPrice",
        },
        {
          oilType: "天然气",
          channel: "气电南山",
          priceValue: null,
          priceField: "hnPrice",
        },
        {
          oilType: "天然气",
          channel: "气电广东",
          priceValue: null,
          priceField: "gdPrice",
        },
      ],
      salesChannels: [
        { id: 1, channel: "香港中电", price: "", expansionCoefficient: "" },
        { id: 2, channel: "气电南山", price: "", expansionCoefficient: "" },
        { id: 3, channel: "气电广东", price: "", expansionCoefficient: "" },
      ],
      channels: ["崖城13-1", "崖城13-10", "陵水17-2", "陵水25-1"],
      selectedChannels: [],
      demandCenters: ["气电南山", "气电广东"],
      selectedDemandCenters: [],
      supplyStrategy: [
        { id: 1, minSupply: "香港中电", channel: "天然气", maxReceive: "" },
        { id: 2, minSupply: "气电南山", channel: "天然气", maxReceive: "" },
        { id: 3, minSupply: "气电广东", channel: "天然气", maxReceive: "" },
      ],
      fieldConstraints: [
        {
          id: 1,
          priceChange: "唐城13-1",
          minProduction: "",
          maxProduction: "",
        },
        {
          id: 2,
          priceChange: "唐城13-10",
          minProduction: "",
          maxProduction: "",
        },
        {
          id: 3,
          priceChange: "陵水17-2",
          minProduction: "",
          maxProduction: "",
        },
        {
          id: 4,
          priceChange: "陵水25-1",
          minProduction: "",
          maxProduction: "",
        },
      ],
      // API响应数据存储
      apiResponseData: [],
      // 装置选择相关
      selectedDevice: "",
      deviceOptions: [],
      currentDeviceData: [],
      // 动态价格计算定时器
      priceCalculationTimer: null,
      columns: [
        { label: "油气类型", prop: "oilType" },
        { label: "渠道", prop: "channel" },
        { label: "崖城13-1", prop: "yancheng13_1" },
        { label: "崖城13-10", prop: "yancheng13_10" },
        { label: "陵水17-2", prop: "lingshui17_2" },
        { label: "陵水25-1", prop: "lingshui25_1" },
      ],
      resultData: [
        // 天然气价格数据
        {
          oilType: "天然气",
          channel: "香港中电",
          yancheng13_1: null, // 对应 hk131gas
          yancheng13_10: null, // 对应 hk1310gas
          lingshui17_2: null, // 对应 hk172gas
          lingshui25_1: null, // 对应 hk251gas
        },
        {
          oilType: "天然气",
          channel: "气电南山",
          yancheng13_1: null, // 对应 hn131gas
          yancheng13_10: null, // 对应 hn1310gas
          lingshui17_2: null, // 对应 hn172gas
          lingshui25_1: null, // 对应 hn251gas
        },
        {
          oilType: "天然气",
          channel: "气电广东",
          yancheng13_1: null, // 对应 gd131gas
          yancheng13_10: null, // 对应 gd1310gas
          lingshui17_2: null, // 对应 gd172gas
          lingshui25_1: null, // 对应 gd251gas
        },
        // 凝析油价格数据
        {
          oilType: "凝析油",
          channel: "", // 清空渠道显示
          yancheng13_1: null, // 占位
          yancheng13_10: null, // 占位
          lingshui17_2: null, // 对应 ls172oil
          lingshui25_1: null, // 占位
          internalChannel: "陵水172", // 内部标识
        },
        {
          oilType: "凝析油",
          channel: "", // 清空渠道显示
          yancheng13_1: null, // 占位
          yancheng13_10: null, // 占位
          lingshui17_2: null, // 占位
          lingshui25_1: null, // 对应 ls251oil
          internalChannel: "陵水251", // 内部标识
        },
        {
          oilType: "凝析油",
          channel: "", // 清空渠道显示
          yancheng13_1: null, // 占位
          yancheng13_10: null, // 对应 yc1310oil
          lingshui17_2: null, // 占位
          lingshui25_1: null, // 占位
          internalChannel: "崖城1310", // 内部标识
        },
        {
          oilType: "凝析油",
          channel: "", // 清空渠道显示
          yancheng13_1: null, // 对应 yc131oil
          yancheng13_10: null, // 占位
          lingshui17_2: null, // 占位
          lingshui25_1: null, // 占位
          internalChannel: "崖城131", // 内部标识
        },
      ],
    };
  },
  watch: {
    // 监听Brent基准价格变化
    "form.brentBenchmark": {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal !== null && newVal !== undefined) {
          this.debouncedCalculateGasPrice();
        }
      },
      immediate: false,
    },
    // 监听汇率变化
    "form.exchangeRate": {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal !== null && newVal !== undefined) {
          this.debouncedCalculateGasPrice();
        }
      },
      immediate: false,
    },
    // 监听Brent/JCC比率变化
    "form.ratio": {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal !== null && newVal !== undefined) {
          this.debouncedCalculateGasPrice();
        }
      },
      immediate: false,
    },
  },
  methods: {
    // 防抖处理的动态价格计算
    debouncedCalculateGasPrice() {
      // 清除之前的定时器
      if (this.priceCalculationTimer) {
        clearTimeout(this.priceCalculationTimer);
      }

      // 设置新的定时器，500ms后执行
      this.priceCalculationTimer = setTimeout(() => {
        this.calculateDynamicGasPrice();
      }, 500);
    },

    // 动态计算天然气价格
    async calculateDynamicGasPrice() {
      // 检查必要的参数是否都已填写
      const { brentBenchmark, exchangeRate, ratio } = this.form;

      if (!brentBenchmark || !exchangeRate || !ratio) {
        return;
      }

      try {
        // 调用calculateGasPrice接口
        const response = await calculateGasPrice({
          brentBenchmark: brentBenchmark,
          exchangeRate: exchangeRate,
          ratio: ratio,
        });

        if (response && response.data) {
          // 更新价格预览表格中"香港中电"行的价格
          this.updateHongKongPrice(response.data);
        } else {
          console.warn("动态价格计算：接口返回数据为空");
        }
      } catch (error) {
        console.error("动态价格计算失败:", error);
        // 不显示错误消息，避免干扰用户输入体验
      }
    },

    // 更新香港中电价格
    updateHongKongPrice(priceData) {
      // 查找价格预览表格中的"香港中电"行
      const hkRow = this.pricePreviewData.find(
        (row) => row.channel === "香港中电"
      );

      if (hkRow) {
        // 假设接口返回的数据结构包含price字段
        // 根据实际接口返回的数据结构调整
        if (typeof priceData === "number") {
          hkRow.priceValue = priceData.toFixed(2);
        }
      } else {
        console.warn("未找到香港中电价格行");
      }
    },

    async startCalculation() {
      // 验证必填字段
      const isValid = await this.validateForm();
      if (!isValid) {
        return;
      }

      try {
        this.$message.info("开始测算...");
        this.isCalculating = true;

        // 从表格数据中提取参数值
        const tableParams = this.extractTableParams();

        // 准备API参数，包含所有35个参数
        const apiParams = {
          // 基础参数
          baseProfit: this.form.baseProfit,
          brentBenchmark: this.form.brentBenchmark,
          brentLowerLimit: this.form.brentLowerLimit,
          brentUpperLimit: this.form.brentUpperLimit,
          exchangeRate: this.form.exchangeRate,
          fullCost: this.form.fullCost,
          minChange: this.form.minChange,
          ratio: this.form.ratio,
          unit: this.form.unit,

          // 从表格中提取的参数
          ...tableParams,
        };

        console.log("API参数:", apiParams);

        // 调用价格敏感性分析接口
        const response = await priceSensitive(apiParams);

        if (response && response.data) {
          this.$message.success("测算完成！");
          // 处理返回的数据
          this.handleCalculationResult(response.data);
        } else {
          this.$message.error("测算失败，请检查参数后重试");
          this.isCalculating = false;
        }
      } catch (error) {
        console.error("价格敏感性分析接口调用失败:", error);
        this.$message.error("测算失败：" + (error.message || "网络错误"));
        this.isCalculating = false;
      }
    },

    extractTableParams() {
      // 从表格数据中提取天然气和凝析油参数
      const params = {};

      this.resultData.forEach((row) => {
        if (row.oilType === "天然气") {
          // 天然气价格参数（从表格获取）
          if (row.channel === "香港中电") {
            params.hk131gas = row.yancheng13_1;
            params.hk1310gas = row.yancheng13_10;
            params.hk172gas = row.lingshui17_2;
            params.hk251gas = row.lingshui25_1;
          } else if (row.channel === "气电南山") {
            params.hn131gas = row.yancheng13_1;
            params.hn1310gas = row.yancheng13_10;
            params.hn172gas = row.lingshui17_2;
            params.hn251gas = row.lingshui25_1;
          } else if (row.channel === "气电广东") {
            params.gd131gas = row.yancheng13_1;
            params.gd1310gas = row.yancheng13_10;
            params.gd172gas = row.lingshui17_2;
            params.gd251gas = row.lingshui25_1;
          }
        } else if (row.oilType === "凝析油") {
          // 凝析油参数（只从表格获取凝析油值）
          if (row.internalChannel === "陵水172") {
            params.ls172oil = row.lingshui17_2;
          } else if (row.internalChannel === "陵水251") {
            params.ls251oil = row.lingshui25_1;
          } else if (row.internalChannel === "崖城1310") {
            params.yc1310oil = row.yancheng13_10;
          } else if (row.internalChannel === "崖城131") {
            params.yc131oil = row.yancheng13_1;
          }
        }
      });

      // 天然气价格参数从价格预览表格中获取
      this.pricePreviewData.forEach((row) => {
        if (row.priceField === "hkPrice") {
          params.hkPrice = row.priceValue;
        } else if (row.priceField === "hnPrice") {
          params.hnPrice = row.priceValue;
        } else if (row.priceField === "gdPrice") {
          params.gdPrice = row.priceValue;
        }
      });

      // 成本占有率参数从costForm的slider获取
      params.ls172Occupancy = this.costForm.lingShui17_2 / 100; // slider值转换为小数
      params.ls251Occupancy = this.costForm.lingShui25_1 / 100;
      params.yc1310Occupancy = this.costForm.yaCheng13_10 / 100;
      params.yc131Occupancy = this.costForm.yaCheng13_1 / 100;

      // 移除基础利润参数（按要求不再需要）

      return params;
    },

    validateForm() {
      return new Promise((resolve) => {
        this.$refs.baseForm.validate((valid) => {
          if (valid) {
            // 额外的业务逻辑验证
            if (this.form.brentLowerLimit >= this.form.brentUpperLimit) {
              this.$message.error("Brent下限必须小于上限");
              resolve(false);
              return;
            }
            if (this.form.exchangeRate <= 0) {
              this.$message.error("汇率必须大于0");
              resolve(false);
              return;
            }
            if (this.form.ratio <= 0) {
              this.$message.error("Brent/JCC比率必须大于0");
              resolve(false);
              return;
            }
            resolve(true);
          } else {
            this.$message.error("请检查并填写必填字段");
            resolve(false);
          }
        });
      });
    },

    handleCalculationResult(data) {
      // 处理接口返回的结果数据
      console.log("计算结果:", data);

      // 统一处理利润数据单位转换：将元转换为万元
      const processedData = (data || []).map(device => ({
        ...device,
        list: (device.list || []).map(item => ({
          ...item,
          profit: parseFloat(item.profit) / 10000 // 将元转换为万元
        }))
      }));

      // 存储处理后的API响应数据
      this.apiResponseData = processedData;

      // 更新装置选项
      this.deviceOptions = this.apiResponseData.map((item) => ({
        name: item.name,
        list: item.list,
      }));

      // 如果有数据，默认选择第一个装置
      if (this.deviceOptions.length > 0) {
        this.selectedDevice = this.deviceOptions[0].name;
        this.updateCurrentDeviceData();
      }
    },

    onDeviceChange() {
      // 装置选择变化时更新表格数据
      this.updateCurrentDeviceData();
    },

    updateCurrentDeviceData() {
      // 根据选中的装置更新当前显示的数据
      const selectedDeviceData = this.apiResponseData.find(
        (item) => item.name === this.selectedDevice
      );

      if (selectedDeviceData && selectedDeviceData.list) {
        this.currentDeviceData = selectedDeviceData.list;
      } else {
        this.currentDeviceData = [];
      }
    },

    resetCalculation() {
      this.isCalculating = false;

      // 重置基础表单数据
      this.form = {
        baseProfit: null,
        brentBenchmark: null,
        brentLowerLimit: null,
        brentUpperLimit: null,
        exchangeRate: null,
        fullCost: null,
        minChange: null,
        ratio: null,
        unit: 1,
      };

      // 重置成本指标数据
      this.costForm = {
        fullCost: "",
        unit: "1",
        yaCheng13_1: 0,
        yaCheng13_10: 0,
        lingShui17_2: 0,
        lingShui25_1: 0,
      };

      // 重置销售预览表格数据
      this.resultData.forEach((row) => {
        row.yancheng13_1 = null;
        row.yancheng13_10 = null;
        row.lingshui17_2 = null;
        row.lingshui25_1 = null;
      });

      // 重置价格预览表格数据
      this.pricePreviewData.forEach((row) => {
        row.priceValue = null;
      });

      // 重置价格表单数据
      this.priceForm = {
        hkPrice: null,
        hnPrice: null,
        gdPrice: null,
      };

      // 重置右侧结果展示数据
      this.apiResponseData = [];
      this.deviceOptions = [];
      this.selectedDevice = "";
      this.currentDeviceData = [];

      this.$message.info("已重置所有参数");
    },

    loadTestData() {
      // 加载基础参数测试数据
      this.form = {
        // 基础参数
        baseProfit: 1000,
        brentBenchmark: 70,
        brentLowerLimit: 50,
        brentUpperLimit: 80,
        exchangeRate: 7.17,
        fullCost: 2200000,
        minChange: 5,
        ratio: 0.95,
        unit: 1,
      };

      // 加载成本指标测试数据
      this.costForm = {
        fullCost: "2200000",
        unit: "1",
        yaCheng13_1: 25, 
        yaCheng13_10: 20, 
        lingShui17_2: 25, 
        lingShui25_1: 30, 
      };

      // 加载销售预览表格测试数据
      this.resultData.forEach((row) => {
        if (row.oilType === "天然气") {
          if (row.channel === "香港中电") {
            row.yancheng13_1 = 500000; // hk131gas
            row.yancheng13_10 = 500000; // hk1310gas
            row.lingshui17_2 = 200000; // hk172gas
            row.lingshui25_1 = 0; // hk251gas
          } else if (row.channel === "气电南山") {
            row.yancheng13_1 = 0; // hn131gas
            row.yancheng13_10 = 0; // hn1310gas
            row.lingshui17_2 = 80000; // hn172gas
            row.lingshui25_1 = 0; // hn251gas
          } else if (row.channel === "气电广东") {
            row.yancheng13_1 = 0; // gd131gas
            row.yancheng13_10 = 0; // gd1310gas
            row.lingshui17_2 = 300000; // gd172gas
            row.lingshui25_1 = 170000; // gd251gas
          }
        } else if (row.oilType === "凝析油") {
          if (row.internalChannel === "陵水172") {
            row.lingshui17_2 = 45.5; // ls172oil
          } else if (row.internalChannel === "陵水251") {
            row.lingshui25_1 = 48.2; // ls251oil
          } else if (row.internalChannel === "崖城1310") {
            row.yancheng13_10 = 42.8; // yc1310oil
          } else if (row.internalChannel === "崖城131") {
            row.yancheng13_1 = 46.3; // yc131oil
          }
        }
      });

      // 加载价格预览表格测试数据
      this.pricePreviewData.forEach((row) => {
        if (row.priceField === "hkPrice") {
          row.priceValue = 2.93; // 香港中电价格
        } else if (row.priceField === "hnPrice") {
          row.priceValue = 1.52; // 气电南山价格
        } else if (row.priceField === "gdPrice") {
          row.priceValue = 2.04; // 气电广东价格
        }
      });

      // 清空图表数据，只加载表单和表格的测试数据
      this.apiResponseData = [];
      this.deviceOptions = [];
      this.selectedDevice = "";
      this.currentDeviceData = [];

      this.$message.success("测试数据已加载");
    },

    saveFormData() {
      // 保存表单和表格数据到本地存储
      try {
        const dataToSave = {
          form: this.form,
          resultData: this.resultData,
        };
        localStorage.setItem(
          "priceSensitivityFormData",
          JSON.stringify(dataToSave)
        );
        this.$message.success("参数已保存到本地");
      } catch (error) {
        this.$message.error("保存失败：" + error.message);
      }
    },

    loadSavedData() {
      // 从本地存储加载保存的数据
      try {
        const savedData = localStorage.getItem("priceSensitivityFormData");
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.form) {
            this.form = parsedData.form;
          }
          if (parsedData.resultData) {
            this.resultData = parsedData.resultData;
          }
          this.$message.info("已加载保存的参数");
        }
      } catch (error) {
        console.error("加载保存数据失败:", error);
      }
    },
  },

  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    // 页面加载时尝试加载保存的数据
    this.loadSavedData();
  },

  beforeDestroy() {
    // 清理定时器
    if (this.priceCalculationTimer) {
      clearTimeout(this.priceCalculationTimer);
      this.priceCalculationTimer = null;
    }
  },
};
</script>

<style scoped>
.price-sensitivity {
  padding: 20px;

  .box-card {
    margin-bottom: 20px;
  }

  .section-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-box {
    display: flex;

    .card-left {
      flex: 1;
      margin-right: 12px;
    }

    .card-right {
      flex: 1;
    }
  }

  .action-buttons {
    text-align: center;
    margin-top: 30px;
    margin-bottom: 20px;
  }

  .chart-box {
    width: 100%;
    height: 400px;
    min-height: 400px;
  }
}

::v-deep .el-card {
  border: 1px solid #edeef5;
}

::v-deep .el-input-group {
  width: 40%;
  .el-select {
    width: 80px;
  }
}

::v-deep .cost-form {
  .el-form-item {
    margin-bottom: 12px;
  }
}

::v-deep .el-slider {
  margin-left: 5%;
  width: 40%;
}

::v-deep .el-col {
  margin-bottom: 12px;
}



/* 浅色主题适配 */
[data-theme="tint"] {
  .price-sensitivity {
    background-color: #ffffff;
    color: #2e3641;
  }

  .box-card {
    background-color: #ffffff;
    border: 1px solid #eaeff5;
  }

  .section-card {
    background-color: #ffffff;
    border: 1px solid #eaeff5;
  }

  /* 标题样式 */
  h1,
  h2,
  h3 {
    color: #2e3641;
  }

  /* 表格样式 */
  ::v-deep .el-table {
    background-color: #ffffff;
  }

  ::v-deep .el-table th {
    background-color: #f5f7fa;
    color: #606266;
  }

  ::v-deep .el-table td {
    background-color: #ffffff;
    color: #2e3641;
  }

  ::v-deep .el-table .el-table__fixed-right,
  ::v-deep .el-table .el-table__fixed-left {
    background-color: #f9f9f9;
  }

  /* 卡片头部样式 */
  ::v-deep .el-card__header {
    background-color: #ffffff;
    border-bottom: 1px solid #eaeff5;
    color: #2e3641;
  }

  /* 卡片内容样式 */
  ::v-deep .el-card__body {
    background-color: #ffffff;
    color: #2e3641;
  }

  /* 表单标签样式 */
  ::v-deep .el-form-item__label {
    color: #606266;
  }

  /* 输入框样式 */
  ::v-deep .el-input__inner {
    background-color: #ffffff;
    border-color: #dcdfe6;
    color: #606266;
  }

  ::v-deep .el-input__inner:focus {
    border-color: #409eff;
  }

  /* 下拉选择框样式 */
  ::v-deep .el-select .el-input__inner {
    background-color: #ffffff;
    border-color: #dcdfe6;
    color: #606266;
  }

  /* 按钮样式 */
  ::v-deep .el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
  }

  ::v-deep .el-button--primary:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }

  ::v-deep .el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;
  }

  ::v-deep .el-button--success:hover {
    background-color: #85ce61;
    border-color: #85ce61;
  }

  ::v-deep .el-button--info {
    background-color: #909399;
    border-color: #909399;
  }

  ::v-deep .el-button--info:hover {
    background-color: #a6a9ad;
    border-color: #a6a9ad;
  }

  /* 表格边框样式 */
  ::v-deep .el-table--border,
  ::v-deep .el-table--group {
    border-color: #eaeff5;
  }

  ::v-deep .el-table td.el-table__cell,
  ::v-deep .el-table th.el-table__cell.is-leaf {
    border-color: #eaeff5;
  }

  /* 修复表格左边框和上边框缺失问题 */
  ::v-deep .el-table::before {
    height: 1px !important;
    background-color: #eaeff5 !important;
  }

  ::v-deep .el-table--border::after {
    width: 1px !important;
    background-color: #eaeff5 !important;
  }

  /* 表格悬停样式 */
  ::v-deep
    .el-table--enable-row-hover
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: #f5f7fa;
  }

  /* 滑块样式 */
  ::v-deep .el-slider__runway {
    background-color: #e4e7ed;
  }

  ::v-deep .el-slider__bar {
    background-color: #409eff;
  }

  ::v-deep .el-slider__button {
    border-color: #409eff;
  }
}

/* 深色主题适配 */
[data-theme="dark"] .price-sensitivity {
  background-color: #0c1324;
  color: #ffffff;

  .box-card {
    background-color: #1a2e52;
    border: 1px solid #4f98f6;
  }

  .section-card {
    background-color: #1a2e52;
    border: 1px solid #4f98f6;
  }

  /* 标题样式 */
  h1,
  h2,
  h3 {
    color: #ffffff;
  }

  /* 表格样式 */
  ::v-deep .el-table {
    background-color: #1a2e52;
  }

  ::v-deep .el-table th {
    background-color: #162549;
    color: #b3d3e5;
  }

  ::v-deep .el-table td {
    background-color: #1a2e52;
    color: #ffffff;
  }

  ::v-deep .el-table .el-table__fixed-right,
  ::v-deep .el-table .el-table__fixed-left {
    background-color: #162549;
  }

  /* 卡片头部样式 */
  ::v-deep .el-card__header {
    background-color: #162549;
    border-bottom: 1px solid #4f98f6;
    color: #ffffff;
  }

  /* 卡片内容样式 */
  ::v-deep .el-card__body {
    background-color: #1a2e52;
    color: #ffffff;
  }

  /* 表单标签样式 */
  ::v-deep .el-form-item__label {
    color: #b3d3e5;
  }

  /* 输入框样式 */
  ::v-deep .el-input__inner {
    background-color: #1a2e52;
    border-color: #4f98f6;
    color: #ffffff;
  }

  ::v-deep .el-input__inner:focus {
    border-color: #6ba4f4;
  }

  /* 下拉选择框样式 */
  ::v-deep .el-select .el-input__inner {
    background-color: #1a2e52;
    border-color: #4f98f6;
    color: #ffffff;
  }

  /* 按钮样式 */
  ::v-deep .el-button--primary {
    background-color: #4f98f6;
    border-color: #4f98f6;
  }

  ::v-deep .el-button--primary:hover {
    background-color: #6ba4f4;
    border-color: #6ba4f4;
  }

  ::v-deep .el-button--success {
    background-color: #52c41a;
    border-color: #52c41a;
  }

  ::v-deep .el-button--success:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }

  ::v-deep .el-button--info {
    background-color: #595959;
    border-color: #595959;
  }

  ::v-deep .el-button--info:hover {
    background-color: #737373;
    border-color: #737373;
  }

  /* 表格边框样式 */
  ::v-deep .el-table--border,
  ::v-deep .el-table--group {
    border-color: #4f98f6;
  }

  ::v-deep .el-table td.el-table__cell,
  ::v-deep .el-table th.el-table__cell.is-leaf {
    border-color: #4f98f6;
  }

  /* 修复表格左边框和上边框缺失问题 */
  ::v-deep .el-table::before {
    height: 1px !important;
    background-color: #4f98f6 !important;
  }

  ::v-deep .el-table--border::after {
    width: 1px !important;
    background-color: #4f98f6 !important;
  }

  /* 表格悬停样式 */
  ::v-deep
    .el-table--enable-row-hover
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: rgba(79, 152, 246, 0.1);
  }

  /* 滑块样式 */
  ::v-deep .el-slider__runway {
    background-color: #4f98f6;
  }

  ::v-deep .el-slider__bar {
    background-color: #6ba4f4;
  }

  ::v-deep .el-slider__button {
    border-color: #6ba4f4;
    background-color: #6ba4f4;
  }

  /* 图表区域样式 */
  .chart-box {
    background-color: #1a2e52;
  }

  /* 测算结果区域背景 */
  .section-box {
    background-color: #1a2e52;
  }

  /* 输入组合框样式 */
  ::v-deep .el-input-group__prepend,
  ::v-deep .el-input-group__append {
    background-color: #162549;
    border-color: #4f98f6;
    color: #b3d3e5;
  }

  /* 下拉选项样式 */
  ::v-deep .el-select-dropdown {
    background-color: #1a2e52;
    border-color: #4f98f6;
  }

  ::v-deep .el-option {
    background-color: #1a2e52;
    color: #ffffff;
  }

  ::v-deep .el-option:hover {
    background-color: rgba(79, 152, 246, 0.1);
  }

  ::v-deep .el-option.selected {
    background-color: #4f98f6;
    color: #ffffff;
  }


}
</style>
