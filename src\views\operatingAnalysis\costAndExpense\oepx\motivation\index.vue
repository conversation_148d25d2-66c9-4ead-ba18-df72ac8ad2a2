<template>
  <div class="factor-analysis-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "FactorAnalysisChart",
  props: {},
  data() {
    return {
      myChart: null,
      // 瀑布图数据
      chartData: [
        { name: "去年同期", value: 22, type: "start" },
        { name: "海上人员费", value: 5, type: "positive" },
        { name: "直升机", value: 8, type: "positive" },
        { name: "供应船", value: 9, type: "positive" },
        { name: "油料", value: 8, type: "positive" },
        { name: "信息通讯气象", value: 2, type: "positive" },
        { name: "维修费用", value: 8, type: "positive" },
        { name: "油气水处理", value: 2, type: "positive" },
        { name: "油井作业费", value: 8, type: "positive" },
        { name: "物流港杂", value: 3, type: "positive" },
        { name: "油气生产研究", value: 8, type: "positive" },
        { name: "保险", value: 22, type: "positive" },
        { name: "健康安全环保", value: 1, type: "positive" },
        { name: "租赁费", value: 8, type: "positive" },
        { name: "其他", value: 6, type: "positive" },
        { name: "去年累计", value: 50, type: "end" }
      ],
      // 响应式配置
      chartWidth: 0,
      isSmallScreen: false
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);
      
      // 计算瀑布图的累积值和显示值
      const processedData = this.processWaterfallData();
      
      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: "16%", // 增加顶部边距为倾斜标签留出空间
          left: "3%",
          right: "4%",
          bottom: this.getBottomMarginForTilted(), // 调整底部边距以适应倾斜标签
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: processedData.categories,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: this.getTiltedLabelFontSize(),
            },
            interval: 0, // 显示所有标签，不进行间隔隐藏
            rotate: this.getTiltedLabelRotate(), // 45度倾斜显示（响应式调整）
            margin: this.getTiltedLabelMargin(),
            // 倾斜标签的对齐方式
            verticalAlign: 'top',
            align: 'right',
            // 确保标签不会被截断
            overflow: 'none',
            // 标签格式化，智能截断过长文本
            formatter: this.formatTiltedLabel,
          },
        },
        yAxis: {
          type: "value",
          name: "亿元",
          nameTextStyle: {
            color: "#ACC2E2",
            align: "right",
            padding: [0, 10, 0, 0],
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
            },
            formatter: "{value}",
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          // 隐藏的辅助系列，用于定位
          {
            name: "辅助",
            type: "bar",
            stack: "总量",
            itemStyle: {
              color: "transparent",
            },
            emphasis: {
              itemStyle: {
                color: "transparent",
              },
            },
            data: processedData.assistData,
            tooltip: {
              show: false,
            },
          },
          // 显示的柱状图系列
          {
            name: "销售收入",
            type: "bar",
            stack: "总量",
            barWidth: "60%",
            itemStyle: {
              color: (params) => {
                // 根据数据类型设置颜色
                const item = this.chartData[params.dataIndex];
                if (item.type === "start") {
                  return "#248EFF"; // 起始值使用深蓝色
                } else if (item.type === "end") {
                  return "#248EFF"; // 结束值使用深蓝色
                } else {
                  return "#58CFFF"; // 增减因子使用浅蓝色
                }
              },
              borderRadius: [2, 2, 2, 2],
            },
            label: {
              show: true,
              position: "top",
              color: "#FFFFFF",
              fontSize: 11,
              fontWeight: "bold",
              formatter: (params) => {
                const item = this.chartData[params.dataIndex];
                if (item.type === "start" || item.type === "end") {
                  return `${item.value}`;
                } else {
                  return item.value > 0 ? `+${item.value}` : `${item.value}`;
                }
              },
            },
            data: processedData.displayData,
          },
        ],
      };

      this.myChart.setOption(option);
    },

    // 处理瀑布图数据
    processWaterfallData() {
      const categories = [];
      const assistData = [];
      const displayData = [];
      let cumulative = 0;

      this.chartData.forEach((item, index) => {
        categories.push(item.name);

        if (item.type === "start") {
          // 起始值
          assistData.push(0);
          displayData.push(item.value);
          cumulative = item.value;
        } else if (item.type === "end") {
          // 结束值 - 计算最终累积值
          const finalValue = cumulative;
          assistData.push(0);
          displayData.push(finalValue);
          // 更新chartData中的最终值以保持一致性
          this.chartData[index].value = finalValue;
        } else {
          // 中间的增减值
          assistData.push(cumulative);
          displayData.push(item.value);
          cumulative += item.value;
        }
      });

      return {
        categories,
        assistData,
        displayData,
      };
    },

    // Tooltip格式化函数
    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';
      
      const dataIndex = params[0].dataIndex;
      const originalData = this.chartData[dataIndex];
      
      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${originalData.name}</div>`;
      
      if (originalData.type === "start" || originalData.type === "end") {
        content += `<div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params[0].color}; border-radius: 50%; margin-right: 8px;"></span>
          <span>金额: ${originalData.value} 亿元</span>
        </div>`;
      } else {
        const sign = originalData.value > 0 ? "+" : "";
        content += `<div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params[0].color}; border-radius: 50%; margin-right: 8px;"></span>
          <span>影响: ${sign}${originalData.value} 亿元</span>
        </div>`;
      }
      
      return content;
    },

    // 响应式处理
    handleResize() {
      if (this.$refs.chartBox) {
        this.chartWidth = this.$refs.chartBox.offsetWidth;
        this.isSmallScreen = this.chartWidth < 800;

        // 重新渲染图表以应用新的配置
        if (this.myChart) {
          this.myChart.resize();
          // 延迟重新初始化以确保尺寸计算正确
          this.$nextTick(() => {
            this.initChart();
          });
        }
      }
    },

    // 获取倾斜标签的字体大小
    getTiltedLabelFontSize() {
      // 倾斜显示时的字体大小，考虑可读性和美观性
      if (this.chartWidth < 480) return 9;  // 极小屏幕使用更小字体
      if (this.chartWidth < 600) return 10;
      if (this.chartWidth < 800) return 11;
      return 12;
    },

    // 获取倾斜标签的旋转角度
    getTiltedLabelRotate() {
      // 根据屏幕大小微调倾斜角度
      if (this.chartWidth < 480) return 50;  // 极小屏幕稍微陡一些
      if (this.chartWidth < 600) return 45;  // 小屏幕标准45度
      return 45; // 其他屏幕使用标准45度
    },

    // 获取倾斜标签的边距（极度优化后）
    getTiltedLabelMargin() {
      // 在极度紧凑布局下精确控制标签边距，确保标签安全显示
      if (this.chartWidth < 480) return 5;  // 最小安全边距
      if (this.chartWidth < 600) return 6;  // 进一步减少边距
      if (this.chartWidth < 800) return 7;  // 中屏幕紧凑边距
      return 8; // 大屏幕最小边距，实现最大向下移动
    },

    // 获取极度优化的底部边距以适应倾斜标签（进一步减少间距）
    getBottomMarginForTilted() {
      // 根据最长标签文本和倾斜角度计算所需空间，实现最大化向下移动
      const maxLabelLength = Math.max(...this.chartData.map(item => item.name.length));

      if (this.chartWidth < 480) {
        // 极小屏幕：进一步减少底部空间，但确保标签安全
        return maxLabelLength > 4 ? "13%" : "9%";
      } else if (this.chartWidth < 600) {
        // 小屏幕：继续减少底部空间
        return maxLabelLength > 4 ? "11%" : "7%";
      } else if (this.chartWidth < 800) {
        // 中等屏幕：更激进的减少底部空间
        return maxLabelLength > 4 ? "9%" : "5%";
      } else {
        // 大屏幕：极限最小化底部空间，实现最大向下移动
        return maxLabelLength > 4 ? "7%" : "3%";
      }
    },

    // 格式化倾斜标签
    formatTiltedLabel(value) {
      // 倾斜显示时的智能文本截断
      if (this.chartWidth < 480) {
        // 极小屏幕：更严格的截断
        if (value.length > 6) {
          return value.substring(0, 4) + '...';
        }
      } else if (this.chartWidth < 600) {
        // 小屏幕
        if (value.length > 8) {
          return value.substring(0, 6) + '...';
        }
      } else if (this.chartWidth < 800) {
        // 中等屏幕
        if (value.length > 10) {
          return value.substring(0, 8) + '...';
        }
      } else {
        // 大屏幕：对于非常长的文本仍需要截断
        if (value.length > 12) {
          return value.substring(0, 10) + '...';
        }
      }
      return value;
    },
  },
};
</script>

<style lang="scss" scoped>
.factor-analysis-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    // 进一步优化高度设置 - 实现最大向下移动的紧凑布局
    min-height: 240px;  // 减少10px
    max-height: 350px;  // 减少20px

    // 响应式高度调整 - 在保持倾斜标签显示的前提下最大化紧凑
    @media (max-width: 768px) {
      min-height: 270px;  // 减少10px
      max-height: 380px;  // 减少20px
    }

    @media (max-width: 480px) {
      min-height: 285px;  // 减少15px
      max-height: 400px;  // 减少20px
    }
  }
}

// 极度优化图表容器间距，实现最大向下移动
@media (max-width: 768px) {
  .factor-analysis-chart {
    .chart-box {
      // 进一步减少底部内边距，实现更贴近底部的效果
      padding-bottom: 8px;  // 减少7px
      padding-top: 5px; // 保持轻微的顶部内边距
    }
  }
}

@media (max-width: 480px) {
  .factor-analysis-chart {
    .chart-box {
      // 小屏幕进一步减少底部空间，在安全范围内最大化向下移动
      padding-bottom: 10px; // 减少8px
      padding-top: 8px; // 小屏幕保持更多顶部空间
    }
  }
}

// 添加大屏幕的优化设置
@media (min-width: 769px) {
  .factor-analysis-chart {
    .chart-box {
      // 大屏幕实现最激进的底部间距减少
      padding-bottom: 5px;  // 最小底部内边距
      padding-top: 3px;     // 轻微顶部内边距
    }
  }
}
</style>
