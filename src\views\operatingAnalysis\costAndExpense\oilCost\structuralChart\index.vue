<template>
  <div class="structural-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "StructuralChart",
  data() {
    return {
      // X轴数据 - 不同的作业公司或油气田
      xData: ["上年同期", "本年累计"],
      // 费用结构数据 - 根据图片中的颜色层次设计
      costData: {
        // 人工费用
        personnel: [8, 6, 5],
        // 材料费用
        materials: [12, 10, 8],
        // 设备费用
        equipment: [6, 8, 7],
        // 维修费用
        maintenance: [4, 5, 6],
        // 运输费用
        transportation: [3, 4, 3],
        // 能源费用
        energy: [5, 6, 4],
      },
      chartInstance: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      // 如果已存在图表实例，先销毁
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }

      this.chartInstance = echarts.init(this.$refs.chartBox);

      const option = {
        // 配色方案 - 参考图片中的多彩堆叠效果
        color: [
          "#FF6B6B", // 红色 - 人工费用
          "#4ECDC4", // 青色 - 材料费用
          "#45B7D1", // 蓝色 - 设备费用
          "#96CEB4", // 绿色 - 维修费用
          "#FFEAA7", // 黄色 - 运输费用
          "#DDA0DD", // 紫色 - 能源费用
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderColor: "rgba(172, 194, 226, 0.2)",
          borderWidth: 1,
          textStyle: {
            color: "#FEFEFF",
            fontSize: 12
          },
          formatter: (params) => {
            let result = `<div style="font-weight: bold; margin-bottom: 8px; color: #FEFEFF;">${params[0].axisValue}</div>`;
            let total = 0;
            
            params.forEach((param) => {
              total += param.value;
              result += `<div style="margin: 3px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 2px; margin-right: 8px;"></span>
                ${param.seriesName}: ${param.value}万元
              </div>`;
            });
            
            result += `<div style="margin-top: 8px; padding-top: 5px; border-top: 1px solid rgba(172, 194, 226, 0.3); color: #FEFEFF; font-weight: bold;">
              总计: ${total}万元
            </div>`;
            
            return result;
          }
        },
        legend: {
          data: ["合计", "SG&A", "弃置费", "DD&A", "其他税项"],
          textStyle: {
            color: "#ffffff",
            fontSize: 11
          },
          icon: "rect",
          itemWidth: 12,
          itemHeight: 8,
          right: "2%",
          top: "2%",
          orient: "vertical"
        },
        grid: {
          left: "8%",
          right: "25%",
          bottom: "6%",
          top: "25%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: this.xData,
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)"
            }
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 11
            },
            interval: 0,
            rotate: 0,
            margin: 8
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: "value",
          name: "费用(万元)",
          nameTextStyle: {
            color: "#ACC2E2",
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)"
            }
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 11
            },
            formatter: "{value}"
          },
          splitLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.1)",
              type: "dashed"
            }
          }
        },
        series: [
          {
            name: "合计",
            type: "bar",
            stack: "cost",
            data: this.costData.personnel,
            barWidth: "60%",
            itemStyle: {
              borderRadius: [0, 0, 0, 0]
            }
          },
          {
            name: "SG&A",
            type: "bar",
            stack: "cost",
            data: this.costData.materials
          },
          {
            name: "弃置费",
            type: "bar",
            stack: "cost",
            data: this.costData.equipment
          },
          {
            name: "DD&A",
            type: "bar",
            stack: "cost",
            data: this.costData.maintenance
          },
          {
            name: "其他税项",
            type: "bar",
            stack: "cost",
            data: this.costData.transportation
          }
        ]
      };

      this.chartInstance.setOption(option);

      // 响应式处理
      this.resizeHandler = () => {
        if (this.chartInstance) {
          this.chartInstance.resize();
        }
      };
      window.addEventListener("resize", this.resizeHandler);
    }
  },
  beforeDestroy() {
    // 清理图表实例和事件监听器
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
    if (this.resizeHandler) {
      window.removeEventListener("resize", this.resizeHandler);
    }
  }
};
</script>

<style lang="scss" scoped>
.structural-chart {
  width: 100%;

  .chart-box {
    width: 100%;
    height: 300px;
  }
}
</style>
