<template>
  <div class="budget-execution">
    <!-- 销售费用图表 -->
    <div class="chart-container">
      <div class="chart-title">销售费用</div>
      <div class="chart-box" ref="salesChartBox"></div>
    </div>
    
    <!-- 管理费用图表 -->
    <div class="chart-container">
      <div class="chart-title">管理费用</div>
      <div class="chart-box admin-chart" ref="adminChartBox"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "BudgetExecution",
  data() {
    return {
      salesChart: null,
      adminChart: null,
      salesData: {
        yData: ["WC16-2", "YC13-10", "LS25-1", "LS17-2", "YC13-1"],
        samePeriodBudget: [300, 250, 1500, 1800, 2800],
        salesExpense: [280, 180, 1400, 1700, 2600],
      },
      adminData: {
        yData: ["崖城机关", "海分机关"],
        samePeriodBudget: [3000, 2500],
        adminExpense: [2800, 2300],
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.salesChart) {
      this.salesChart.dispose();
    }
    if (this.adminChart) {
      this.adminChart.dispose();
    }
  },
  methods: {
    initCharts() {
      this.initSalesChart();
      this.initAdminChart();
    },

    initSalesChart() {
      this.salesChart = echarts.init(this.$refs.salesChartBox);
      const option = this.getChartOption(this.salesData, "销售费用");
      this.salesChart.setOption(option);
    },

    initAdminChart() {
      if (this.adminChart) {
        this.adminChart.dispose();
      }

      this.adminChart = echarts.init(this.$refs.adminChartBox);
      const option = this.getChartOption(this.adminData, "管理费用");
      this.adminChart.setOption(option);
    },

    getChartOption(data, expenseType) {
      const isAdminExpense = expenseType === "管理费用";
      const categoryGap = isAdminExpense ? "0.5%" : "20%";

      return {
        color: ["#FFA500", "#248EFF", "#52C41A"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          borderColor: "rgba(172, 194, 226, 0.2)",
          borderWidth: 1,
          textStyle: {
            color: "#FEFEFF",
            fontSize: 12,
          },
          formatter: (params) => {
            const dataIndex = params[0].dataIndex;
            const budgetValue = data.samePeriodBudget[dataIndex];
            const expenseValue = expenseType === "销售费用" ? data.salesExpense[dataIndex] : data.adminExpense[dataIndex];
            const completionRate = ((expenseValue / budgetValue) * 100).toFixed(2);

            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: #FEFEFF;">${params[0].axisValue}</div>`;

            params.forEach((param) => {
              result += `<div style="margin: 2px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${Number(param.value).toFixed(0)}万元
              </div>`;
            });

            result += `<div style="margin: 2px 0; color: #FEFEFF;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: #FFA500; border-radius: 50%; margin-right: 5px;"></span>
              同期预算完成率: ${completionRate}%
            </div>`;

            return result;
          },
        },
        legend: {
          data: ["同期预算", expenseType],
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
          icon: "rect",
          itemWidth: 8,
          itemHeight: 8,
          right: "10%",
          top: "2%",
        },
        grid: {
          left: "4%",
          right: "4%",
          bottom: isAdminExpense ? "25%" : "3%",
          top: isAdminExpense ? "15%" : "10%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.3)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 11,
            },
            formatter: "{value}",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.15)",
              type: "dashed",
            },
          },
        },
        yAxis: {
          type: "category",
          data: data.yData,
          inverse: true,
          boundaryGap: isAdminExpense ? [categoryGap, categoryGap] : true,
          splitNumber: isAdminExpense ? 2 : undefined,
          nameTextStyle: {
            color: "#ACC2E2",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#ACC2E2",
              fontSize: 11,
            },
            margin: 8,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: "同期预算",
            type: "bar",
            data: data.samePeriodBudget,
            barWidth: 15,
            barMaxWidth: 15,
            barMinHeight: 0,
            barCategoryGap: categoryGap,
            itemStyle: {
              color: "#248EFF",
              borderRadius: [0, 8, 8, 0],
            },
            label: {
              show: false,
            },
            z: 1,
          },
          {
            name: expenseType,
            type: "bar",
            data: expenseType === "销售费用" ? data.salesExpense : data.adminExpense,
            barWidth: 10,
            barMaxWidth: 10,
            barMinHeight: 0,
            barCategoryGap: categoryGap,
            itemStyle: {
              color: "#7262FD",
              borderRadius: [0, 6, 6, 0],
            },
            label: {
              show: false,
            },
            z: 2,
          },
        ],
      };
    },

    handleResize() {
      if (this.salesChart) {
        this.salesChart.resize();
      }
      if (this.adminChart) {
        this.adminChart.resize();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.budget-execution {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 6px;

  .chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 2px 0;

    .chart-title {
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #FEFEFF;
      padding: 12px 0;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      flex-shrink: 0;
    }

    .chart-box {
      flex: 1;
      width: 98%;
      height: 300px;
      margin: 0 auto;

      &.admin-chart {
        height: 120px !important;
        min-height: 120px;
      }
    }
  }
}

[data-theme="dark"] .budget-execution {
  .chart-container {
    .chart-title {
      color: #FEFEFF;
      padding: 12px 0;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
