import { instance } from "../config";

// 化学药剂费用执行报表
export function getExecutExpenses(data) {
  return instance({
    url: "/waterhandleForms/reportForms",
    method: "post",
    data,
  });
}

export function saveExecutExpenses(data) {
  return instance({
    url: "/waterhandleForms/save",
    method: "post",
    data,
  });
}

// 药剂服务费用执行报表
export function getServiceFee(data) {
  return instance({
    url: "/chemicalServe/report",
    method: "post",
    data,
  });
}

export function saveServiceFee(data) {
  return instance({
    url: "/chemicalServe/saveReport",
    method: "post",
    data,
  });
}

// 药剂分析化验费用执行报表
export function getTestFee(data) {
  return instance({
    url: "/ChemicalAnalysis/report",
    method: "post",
    data,
  });
}

export function saveTestFee(data) {
  return instance({
    url: "/ChemicalAnalysis/saveReport",
    method: "post",
    data,
  });
}

// 报告生成
export function getReport(data) {
  return instance({
    url: "/export/word",
    method: "post",
    responseType: "blob",
    data,
  })
}
