/**
 * 文件下载工具函数
 * 支持常用文件格式的下载，使用简单的文件类型标识符
 *
 * @param {ArrayBuffer|Blob} fileData - 文件数据
 * @param {string} fileName - 文件名（包含扩展名），默认为"file.pdf"
 * @param {string} fileType - 文件类型标识符，默认为"pdf"
 *                           支持的类型：pdf, doc, docx
 *
 * @example
 * // 下载PDF文件
 * downFileUtil(pdfData, "report.pdf", "pdf");
 *
 * // 下载Word文件
 * downFileUtil(wordData, "document.docx", "docx");
 * downFileUtil(wordData, "document.doc", "doc");
 *
 * // 使用默认类型（PDF）
 * downFileUtil(pdfData, "report.pdf"); // 默认为PDF类型
 */
const downFileUtil = (fileData, fileName = "file.pdf", fileType = "pdf") => {
  // 文件类型到MIME类型的映射
  const mimeTypeMap = {
    "pdf": "application/pdf",
    "doc": "application/msword",
    "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  };

  // 获取MIME类型，如果不支持则使用默认值
  const mimeType = mimeTypeMap[fileType] || "application/octet-stream";

  // 创建Blob对象
  const blob = new Blob([fileData], { type: mimeType });

  // 创建下载链接
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;

  // 触发点击下载
  document.body.appendChild(a);
  a.click();

  // 清理
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};

export { downFileUtil };
