<template>
  <div class="oilCost">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分项统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="structural-proportion">
        <chartBox :title="'结构占比'">
            <CarouselBtn :buttons="buttons" />
            <StructuralChart/>
        </chartBox>
      </div>
      <div class="motivation-box">
        <chartBox :title="'同比增减动因'">
            <CarouselBtn :buttons="buttons" />
            <MotivationChart/>
        </chartBox>
      </div>
      <div class="analysis-box">
        <chartBox :title="'分油气田分析'">
            <ButtonBox :buttons="ogsBtns"></ButtonBox>
            <SubItemCost/>
            <!-- <FiveCosts/> -->
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import ItemCard from "../../components/ItemCard.vue";
import ButtonBox from "@/views/businessAnalysis/prodEnviTrack/buttonBox.vue";
import StructuralChart from "./structuralChart/index.vue"
import MotivationChart from "./motivationChart/index.vue"
import FiveCosts from "./fiveCosts/index.vue"
import SubItemCost from "./subItemCost/index.vue"
export default {
  name: "oilCost",
  components: {
    CommonTable,
    CarouselBtn,
    DatePicker,
    ItemCard,
    ButtonBox,
    StructuralChart,
    MotivationChart,
    FiveCosts,
    SubItemCost
  },
  data() {
    return {
      newDateValue: "",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "总计", value: "100" },
        { title: "自控", value: "100" },
        { title: "非自控", value: "100" },
      ],
      colums:[
        {label:'项目($/boe)', prop:'project'},
        {label:'本年累计',prop:'thisYear'},
        {label:'同期预算',prop:'budget'},
        {label:'预算完成率',prop:'rate'},
        {label:'去年同期',prop:'lastYear'},
        {label:'同比变动',prop:'change'},
      ],
      tableData:[
        {project:'作业费',thisYear:'100',budget:'100',rate:'100',lastYear:'100',change:'100'},
        {project:'管理费',thisYear:'100',budget:'100',rate:'100',lastYear:'100',change:'100'},
      ],
      ogsBtns: ["分项成本", "五项成本"],
    };
  },
};
</script>
<style lang="scss" scoped>
.oilCost {
  .content-up {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    gap: 10px;
    .main-indicators {
      flex: 1;
      min-width: 0;

      .card-box {
        display: flex;
        justify-content: space-between;
        margin: 8px 20px; // 减少边距，避免内容溢出
        flex: 1; // 占据剩余空间
        min-height: 0; // 允许flex收缩
        gap: 12px; // 使用gap替代margin-right

        .item-card {
          flex: 1;
          min-width: 0; // 允许收缩
          height: 100%; // 确保卡片填满容器高度
        }
      }
    }

    .statistics-box {
      flex: 1;
      min-width: 0;

      .table-box {
        margin: 12px 16px; // 减少上下边距
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .content-down {
    display: flex;
    justify-content: space-between;
    gap: 10px;

    .structural-proportion {
      flex: 1;
      min-width: 0;
    }
    .motivation-box {
      flex: 1;
      min-width: 0;
    }
    .analysis-box {
      flex: 1;
      min-width: 0;
    }
  }
}
</style>
