<template>
  <div class="cost-statistics">
    <div class="search-header">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      ></OgwSearch>
      <div class="group-btn">
        <el-button
          type="primary"
          @click="handleReport"
          :loading="reportLoading"
          :disabled="reportLoading"
        >
          {{ reportLoading ? '正在生成报告...' : '报告生成' }}
        </el-button>
        <el-button type="primary" disabled>导出</el-button>
      </div>
    </div>
    <div>
      <div class="title">
        {{ titleName }}油气水处理年度费用执行报表(截止{{ date }})
      </div>
      <div class="table-title">
        {{ titleName }}化学药剂费用执行(截止{{ date }})
      </div>
      <OgwTable
        :data="exTableData"
        :columns="exColumns"
        :summary-config="exSummaryConfig"
        :loading="loading1"
        :show-index="true"
        :showActions="true"
        :merge-keys="['productionName']"
        :height="600"
        @cell-click="exInputReason"
        @save-row="(row) => saveRow(row, 'ex')"
      >
        <template #reason="{ row, $index }">
          {{ row.reason || "无" }}
        </template>
        <template #actions="{ row, $index }">
          <el-button
            type="text"
            size="small"
            @click="saveRow(row, (type = 'ex'))"
            >保存</el-button
          >
        </template>
      </OgwTable>
      <div class="table-title">
        {{ titleName }}药剂服务费用执行(截止{{ date }})
      </div>
      <OgwTable
        :loading="loading2"
        :data="serTableData"
        :columns="serColumns"
        :summary-config="serSummaryConfig"
        :show-index="true"
        :merge-keys="['productionName']"
        :showActions="true"
        @cell-click="serInputReason"
        @save-row="(row) => saveRow(row, 'ser')"
      >
        <template #reason="{ row, $index }">
          {{ row.reason || "无" }}
        </template>
        <template #actions="{ row, $index }">
          <el-button
            type="text"
            size="small"
            @click="saveRow(row, (type = 'ser'))"
            >保存</el-button
          >
        </template>
      </OgwTable>
      <div class="table-title">{{ titleName }}分析化验费用(截止{{ date }})</div>
      <OgwTable
        :loading="loading3"
        :data="testTableData"
        :columns="testColumns"
        :summary-config="testSummaryConfig"
        :show-index="true"
        :merge-keys="['productionName']"
        :showActions="true"
        @cell-click="testInputReason"
        @save-row="(row) => saveRow(row, 'test')"
      >
        <template #reason="{ row, $index }">
          {{ row.reason || "无" }}
        </template>
        <template #actions="{ row, $index }">
          <el-button
            type="text"
            size="small"
            @click="saveRow(row, (type = 'test'))"
            >保存</el-button
          >
        </template>
      </OgwTable>
    </div>
    <el-dialog
      :visible.sync="showInput"
      v-if="showInput"
      title="执行差异原因"
      width="600px"
      custom-class="reason-input-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="dialog-content">
        <div class="input-label warning-text">请输入执行差异原因：</div>
        <el-input
          v-model.trim="reasonInfo"
          type="textarea"
          :rows="5"
          placeholder="请详细说明执行差异的具体原因..."
          maxlength="500"
          show-word-limit
          class="reason-textarea"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="referenceLastInput" class="reference-btn">参考上次填写</el-button>
        <el-button @click="showInput = false" class="cancel-btn">取 消</el-button>
        <el-button type="primary" @click="confirmInput" class="confirm-btn">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import { getToday } from "@/utils/index";
import {
  getExecutExpenses,
  getServiceFee,
  getTestFee,
  saveExecutExpenses,
  saveServiceFee,
  saveTestFee,
  getReport,
} from "@/api/ogwWatch/costStatistics.js";
import { getProd } from "@/api/common.js";
import {downFileUtil} from "@/utils/file.js";
export default {
  name: "costStatistics",
  components: {
    OgwSearch,
    OgwTable,
  },
  async mounted() {
    this.searchForm.endTime = getToday().YM2;
    await this.getProdList();
    this.loadReasonHistory();
    this.getExTableData();
    this.getSerTableData();
    this.getTestTableData();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    titleName() {
      const orgName = this.searchForm.orgName;
      return orgName;
    },
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
        {
          label: "截止:",
          prop: "endTime",
          type: "month",
        },
      ];
    },
    date() {
      return this.searchForm.endTime;
    },
    exColumns() {
      const year = this.searchForm.endTime.split("-")[0];
      const lastMonth = Number(this.searchForm.endTime.split("-")[1]);

      return [
        { label: "平台名称", prop: "productionName" },
        { label: "药剂名称 ", prop: "chemicalName" },
        { label: "药剂型号", prop: "chemicalType" },
        {
          label: `${year}年度1-${lastMonth}月实际消耗(升)`,
          prop: "injectionVolumeSum",
          editable: true,
          formatter: (row) => {
            const value = row.injectionVolumeSum;
            if (
              value === "" ||
              value === null ||
              value === undefined ||
              isNaN(value)
            ) {
              return 0;
            }
            return Number(value).toFixed(2);
          },
        },
        {
          label: `${year}年预算金额(万元)`,
          prop: "budgetcostytd",
          editable: true,
          formatter: (row) => {
            return this.safeFormat(row.budgetcostytd);
          },
        },
        {
          label: `${year}年1-${lastMonth}月预算金额(万元)`,
          prop: "budgetcostmonth",
          editable: true,
          formatter: (row) => {
            return this.safeFormat(row.budgetcostmonth);
          },
        },
        {
          label: `${year}年1-${lastMonth}月实际金额(万元)`,
          prop: "finalcostmonth",
          editable: true,
          formatter: (row) => {
            return this.safeFormat(row.finalcostmonth);
          },
        },
        {
          label: `${year}年1-${lastMonth}月药剂出库费用(万元)`,
          prop: "outboundCost",
          editable: true,
          formatter: (row) => {
            return this.safeFormat(row.outboundCost);
          },
        },
        {
          label: "处理气量(亿方)",
          prop: "airVolume",
          formatter: (row) => {
            return this.safeFormat(row.airVolume);
          },
        },
        {
          label: "处理水量(方)",
          prop: "waterVolume",
          formatter: (row) => {
            return this.safeFormat(row.waterVolume);
          },
        },
        {
          label: "处理油量(方)",
          prop: "oilVolume",
          formatter: (row) => {
            return this.safeFormat(row.oilVolume);
          },
        },
        {
          label: "推荐药剂应加注浓度(ppm)",
          prop: "reConcentration",
        },
        {
          label: "药剂实际加注浓度(ppm)",
          prop: "concentration",
        },
        {
          label: `${year - 1}年同期药剂实际加注浓度`,
          prop: "concentrationLastYear",
        },
        {
          label: "执行差异说明",
          prop: "reason",
          clickable: true,
        },
      ];
    },
    serColumns() {
      const headTitle = `预算金额(截止${this.searchForm.endTime})(万元)`;
      return [
        { label: "平台名称", prop: "productionName" },
        { label: "服务项目", prop: "projectName" },
        {
          label: "服务时间",
          prop: "serviceTime",
          formatter: (row) => {
            if (!row.serviceTime) return "";
            return row.serviceTime.split(" ")[0];
          },
        },
        {
          label: `${headTitle}`,
          prop: "budgetCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "已执行金额(万元)",
          prop: "finalCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "执行差异原因",
          prop: "reason",
          clickable: true,
        },
      ];
    },
    testColumns() {
      const headTitle = `预算金额(截止${this.searchForm.endTime})(万元)`;
      return [
        { label: "平台名称", prop: "productionName" },
        { label: "项目名称", prop: "projectName" },
        {
          label: "服务时间",
          prop: "serviceTime",
          formatter: (row) => {
            if (!row.serviceTime) return "";
            return row.serviceTime.split(" ")[0];
          },
        },
        {
          label: `${headTitle}`,
          prop: "budgetCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "已执行金额(万元)",
          prop: "finalCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "执行差异原因",
          prop: "reason",
          clickable: true,
        },
      ];
    },
  },
  data() {
    return {
      serTotal: {},
      exTotal: {},
      testTotal: {},
      loading1: false,
      loading2: false,
      loading3: false,
      reportLoading: false, // 报告生成loading状态
      orgList: [],
      deviceOptions: [],
      showInput: false,
      reasonInfo: "",
      reasonRowId: "", //记录是哪行表格数据
      reasonType: "", //记录是哪个表格
      lastReasonHistory: {}, //存储上次填写的原因历史记录
      searchForm: {
        orgId: "",
        deviceNameCode: "",
        orgName: "",
        endTime: "",
      },
      exTableData: [],
      exSummaryConfig: {
        groupField: "productionName", // 分组字段
        sumColumns: [
          "injectionVolumeSum",
          "budgetcostytd",
          "finalcostmonth",
          "budgetcostmonth",
          "outboundCost",
          "airVolume",
          "waterVolume",
          "oilVolume",
        ], // 需要计算的列
        grandTotalText: "合计",
        subTotalText: "小计",
        customSubTotalText: "小计(sap)",
        customGrandTotalText: "总计(sap)",
        showSubTotal: true, // 是否显示小计
        showGrandTotal: true, // 是否显示总计
        showCustomSubTotal: true,
        showCustomGrandTotal: true, // 是否显示自定义总计
        subTotalTextMergeColumns: ["chemicalName", "chemicalType"], // 小计合并的列
        customSubTotalTextMergeColumns: ["chemicalName", "chemicalType"],
        grandTotalTextMergeColumns: [
          "productionName",
          "chemicalName",
          "chemicalType",
        ], // 总计合并的列
        customGrandTotalTextMergeColumns: [
          "productionName",
          "chemicalName",
          "chemicalType",
        ],
        customSubTotalData: (group, row) => {
          const customData = row?.customData || {};
          return {
            finalcostmonth: customData.finalcostmonth || 0,
            budgetcostytd: customData.budgetcostytd || 0,
            budgetcostmonth: customData.budgetcostmonth || 0,
          };
        },
        customGrandTotalData: () => {
          return {
            finalcostmonth: this.safeFormat(this.exTotal.finalcostmonth),
            budgetcostytd: this.safeFormat(this.exTotal.budgetcostytd),
            budgetcostmonth: this.safeFormat(this.exTotal.budgetcostmonth),
          };
        },
      },
      serTableData: [],
      serSummaryConfig: {
        groupField: "productionName",
        sumColumns: ["budgetCost", "finalCost"],
        grandTotalText: "合计",
        subTotalText: "小计",
        customSubTotalText: "小计(sap)",
        customGrandTotalText: "总计(sap)",
        showSubTotal: true,
        showGrandTotal: true,
        showCustomSubTotal: true,
        showCustomGrandTotal: true,
        subTotalTextMergeColumns: ["projectName", "serviceTime"],
        customSubTotalTextMergeColumns: ["projectName", "serviceTime"],
        grandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customGrandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customSubTotalData: (group, row) => {
          const customData = row?.customData || {};
          return {
            budgetCost: customData.budgetCost || 0,
            finalCost: customData.finalCost || 0,
          };
        },
        customGrandTotalData: () => {
          return {
            budgetCost: this.serTotal.budget || 0,
            finalCost: this.serTotal.final || 0,
          };
        },
      },
      testTableData: [],
      testSummaryConfig: {
        groupField: "productionName",
        sumColumns: ["budgetCost", "finalCost"],
        grandTotalText: "合计",
        subTotalText: "小计",
        customSubTotalText: "小计(sap)",
        customGrandTotalText: "总计(sap)",
        showSubTotal: true,
        showGrandTotal: true,
        showCustomSubTotal: true,
        showCustomGrandTotal: true,
        subTotalTextMergeColumns: ["projectName", "serviceTime"],
        customSubTotalTextMergeColumns: ["projectName", "serviceTime"],
        grandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customGrandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customSubTotalData: (group, row) => {
          const customData = row?.customData || {};
          return {
            budgetCost: customData.budgetCost || 0,
            finalCost: customData.finalCost || 0,
          };
        },
        customGrandTotalData: (allData) => {
          return {
            budgetCost: this.testTotal.budget || 0,
            finalCost: this.testTotal.final || 0,
          };
        },
      },
    };
  },
  methods: {
    // 报告生成
   async handleReport() {
      try {
        // 开始loading
        this.reportLoading = true;

        const data = {
          endDate: this.searchForm.endTime,
          productionUnitIds: this.searchForm.deviceNameCode,
        }
        const res = await getReport(data);
        if(res){
          const fileName = `${this.titleName} 油气水处理年度费用执行报表`
          downFileUtil(res,fileName,"docx")
          this.$message.success('报告生成成功！');
        } else {
          this.$message.error('报告生成失败，请重试');
        }
      } catch (error) {
        console.error('报告生成失败:', error);
        this.$message.error('报告生成失败，请重试');
      } finally {
        // 结束loading
        this.reportLoading = false;
      }
    },
    // 安全的数值处理方法
    safeNumber(value) {
      if (
        value === "" ||
        value === null ||
        value === undefined ||
        isNaN(value)
      ) {
        return 0;
      }
      return Number(value);
    },

    // 安全的数值格式化方法
    safeFormat(value, decimals = 2) {
      const num = this.safeNumber(value);
      return num.toFixed(decimals);
    },

    // 加载历史记录
    loadReasonHistory() {
      try {
        const savedHistory = localStorage.getItem('costStatistics_reasonHistory');
        if (savedHistory) {
          this.lastReasonHistory = JSON.parse(savedHistory);
        }
      } catch (error) {
        console.warn('加载历史记录失败:', error);
        this.lastReasonHistory = {};
      }
    },

    exInputReason({ row, prop, index, value }) {
      // console.log("exInputReason", row, prop, index, value);
      this.reasonRowId = row.id;
      this.showInput = true;
      this.reasonInfo = value;
      this.reasonType = "ex";
    },
    serInputReason({ row, prop, index, value }) {
      // console.log("serInputReason", row, prop, index, value);
      this.reasonRowId = row.serveId;
      this.showInput = true;
      this.reasonInfo = value;
      this.reasonType = "ser";
    },
    testInputReason({ row, prop, index, value }) {
      this.reasonRowId = row.serveId;
      this.showInput = true;
      this.reasonInfo = value;
      this.reasonType = "test";
    },
    // 参考上次填写功能
    referenceLastInput() {
      const historyKey = `${this.reasonType}_${this.reasonRowId}`;
      const lastReason = this.lastReasonHistory[historyKey];

      if (lastReason) {
        this.reasonInfo = lastReason;
        this.$message.success("已填入上次的执行差异原因");
      } else {
        // 如果没有历史记录，尝试查找同类型的最近一次填写
        const typeHistory = Object.keys(this.lastReasonHistory)
          .filter(key => key.startsWith(this.reasonType + '_'))
          .map(key => this.lastReasonHistory[key])
          .filter(reason => reason && reason.trim() !== '');

        if (typeHistory.length > 0) {
          // 使用最近一次的填写内容
          this.reasonInfo = typeHistory[typeHistory.length - 1];
          this.$message.success("已填入同类型最近一次的执行差异原因");
        } else {
          this.$message.warning("暂无历史填写记录可供参考");
        }
      }
    },

    confirmInput() {
      // 保存到历史记录
      if (this.reasonInfo && this.reasonInfo.trim() !== '') {
        const historyKey = `${this.reasonType}_${this.reasonRowId}`;
        this.lastReasonHistory[historyKey] = this.reasonInfo;

        // 将历史记录保存到本地存储
        try {
          localStorage.setItem('costStatistics_reasonHistory', JSON.stringify(this.lastReasonHistory));
        } catch (error) {
          console.warn('保存历史记录失败:', error);
        }
      }

      this.showInput = false;
      switch (this.reasonType) {
        case "ex":
          this.exTableData.forEach((item) => {
            if (item.id === this.reasonRowId) {
              item.reason = this.reasonInfo;
            }
          });
          break;
        case "ser":
          // 给对应行的reason赋值
          this.serTableData.forEach((item) => {
            if (item.serveId === this.reasonRowId) {
              item.reason = this.reasonInfo;
            }
          });
          break;
        case "test":
          this.testTableData.forEach((item) => {
            if (item.serveId === this.reasonRowId) {
              item.reason = this.reasonInfo;
            }
          });
          break;
      }
    },
    handleSearch(value) {
      this.searchForm.orgId = value?.orgId;
      this.searchForm.deviceNameCode = value?.deviceNameCode;
      this.searchForm.orgName = value?.orgName; //保存标题
      this.searchForm.endTime = value.endTime;
      this.getExTableData();
      this.getSerTableData();
      this.getTestTableData();
    },
    handleReset(value) {
      // 重置搜索表单
      this.searchForm = {
        orgId: "",
        deviceNameCode: "",
        orgName: "",
        endTime: getToday().YM2,
      };
      this.getExTableData();
      this.getSerTableData();
      this.getTestTableData();
    },
    async saveRow(row, type) {
      switch (type) {
        case "ex":
          const resE = await saveExecutExpenses(row);
          if (resE.code === 200) {
            this.$message.success("保存成功");
            this.getExTableData();
          }
          break;
        case "ser":
          row.id = row.serveId;
          const resS = await saveServiceFee(row);
          if (resS.code === 200) {
            this.$message.success("保存成功");
            this.getSerTableData();
          }
          break;
        case "test":
          row.id = row.serveId;
          const resT = await saveTestFee(row);
          if (resT.code === 200) {
            this.$message.success("保存成功");
            this.getTestTableData();
          }
          break;
      }
    },
    async getExTableData() {
      this.exTableData = [];

      this.loading1 = true;
      try {
        const data = {
          productionUnitIds: this.searchForm.deviceNameCode || "",
          endDate: this.searchForm.endTime || null,
        };
        const res = await getExecutExpenses(data);
        if (res.code === 200) {
          // 获取总计
          this.exTotal = res.data.reduce(
            (acc, cur) => {
              acc.budgetcostytd += this.safeNumber(cur.budgetcostytdSap);
              acc.finalcostmonth += this.safeNumber(cur.finalcostmonthSap);
              acc.budgetcostmonth += this.safeNumber(cur.budgetcostmonthSap);
              return acc;
            },
            {
              budgetcostytd: 0,
              finalcostmonth: 0,
              budgetcostmonth: 0,
            }
          );
          this.exTableData = res.data.map((item) => {
            item.list.forEach((i) => {
              // 处理空值和数据类型转换
              i.customData = {
                finalcostmonth: this.safeNumber(item.finalcostmonthSap),
                budgetcostytd: this.safeNumber(item.budgetcostytdSap),
                budgetcostmonth: this.safeNumber(item.budgetcostmonthSap),
              };

              // 处理list中的数值字段
              i.injectionVolumeSum = this.safeNumber(i.injectionVolumeSum);
              i.budgetcostytd = this.safeNumber(i.budgetcostytd);
              i.budgetcostmonth = this.safeNumber(i.budgetcostmonth);
              i.finalcostmonth = this.safeNumber(i.finalcostmonth);
              i.outboundCost = this.safeNumber(i.outboundCost);
              i.airVolume = this.safeNumber(i.airVolume);
              i.waterVolume = this.safeNumber(i.waterVolume);
              i.oilVolume = this.safeNumber(i.oilVolume);

              // 处理浓度字段，保持原始值但确保不为null
              i.concentration = i.concentration || "";
              i.reConcentration = i.reConcentration || "";
              i.concentrationLastYear = i.concentrationLastYear || "";
              i.reason = i.reason || "";
            });
            return item.list;
          });
          this.exTableData = this.exTableData.flat();
        }
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("数据加载失败，请稍后重试");
      } finally {
        this.loading1 = false;
      }
    },
    async getSerTableData() {
      this.serTableData = [];
      this.loading2 = true;
      try {
        const data = {
          productionUnitIds: this.searchForm.deviceNameCode || "",
          endDate: this.searchForm.endTime || null,
        };
        const res = await getServiceFee(data);
        if (res.code === 200) {
          // 获取分组数据的budget总计和final总计(传入的budget可能是""或null)
          this.serTotal = res.data.reduce(
            (acc, cur) => {
              acc.budget += this.safeNumber(cur.budget);
              acc.final += this.safeNumber(cur.final);
              return acc;
            },
            { budget: 0, final: 0 }
          );
          this.serTableData = res.data.map((item) => {
            item.list.forEach((i) => {
              i.customData = {
                budgetCost: item.budget,
                finalCost: item.final,
              };
            });
            return item.list;
          });
        }
        this.serTableData = this.serTableData.flat();
      } catch (error) {
        this.serTableData = [];
        console.error("获取数据失败:", error);
        this.$message.error("数据加载失败");
      } finally {
        this.loading2 = false;
      }
    },
    async getTestTableData() {
      this.testTableData = [];
      this.loading3 = true;
      try {
        const data = {
          productionUnitIds: this.searchForm.deviceNameCode || "",
          endDate: this.searchForm.endTime || null,
        };
        const res = await getTestFee(data);
        if (res.code === 200) {
          // 获取分组数据的budget总计和final总计(传入的budget可能是""或null)
          this.testTotal = res.data.reduce(
            (acc, cur) => {
              acc.budget += this.safeNumber(cur.budget);
              acc.final += this.safeNumber(cur.final);
              return acc;
            },
            { budget: 0, final: 0 }
          );
          this.testTableData = res.data.map((item) => {
            item.list.forEach((i) => {
              i.customData = {
                budgetCost: item.budget,
                finalCost: item.final,
              };
            });
            return item.list;
          });
        }
        this.testTableData = this.testTableData.flat();
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("数据加载失败");
      } finally {
        this.loading3 = false;
      }
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
        this.searchForm.orgId = this.orgList[0].orgId;
        this.searchForm.orgName = this.orgList[0].orgName;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.cost-statistics {
  padding: 0 16px;
  padding-bottom: 20px;
  .search-header {
    display: flex;
    justify-content: space-between;
    .group-btn {
      margin: 20px 0;
    }
  }

  .title {
    font-size: 22px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
  }

  .table-title {
    margin-top: 35px;
    margin-bottom: 3px;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
  }
}

[data-theme="dark"] .cost-statistics {
  background: #162549;
  .title {
    color: #fff;
  }

  .table-title {
    color: #fff;
  }
}

[data-theme="tint"] .cost-statistics {
  background: #fff;
  .title {
    color: rgba(0, 0, 0, 0.85);
  }

  .table-title {
    color: rgba(0, 0, 0, 0.85);
  }
}

/* 弹窗样式优化 - 统一浅色模式 */
.reason-input-dialog {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  background: #ffffff !important;

  .el-dialog__header {
    background: linear-gradient(135deg, #4ea0fd 0%, #409eff 100%) !important;
    padding: 20px 24px;
    border-bottom: none;

    .el-dialog__title {
      color: #ffffff !important;
      font-size: 18px;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .el-dialog__close {
      color: #ffffff !important;
      font-size: 20px;
      opacity: 0.8;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: #ffffff !important;

    .dialog-content {
      .input-label {
        font-size: 14px;
        font-weight: 500;
        color: #333333 !important;
        margin-bottom: 12px;
        line-height: 1.5;

        &.warning-text {
          color: #ff4d4f !important;
          font-weight: 600;
        }
      }

      .reason-textarea {
        .el-textarea__inner {
          border: 2px solid #e8f4fd !important;
          border-radius: 8px;
          padding: 12px 16px;
          font-size: 14px;
          line-height: 1.6;
          resize: vertical;
          transition: all 0.3s ease;
          background: #fafbfc !important;
          color: #333333 !important;

          &:focus {
            border-color: #4ea0fd !important;
            background: #ffffff !important;
            box-shadow: 0 0 0 3px rgba(78, 160, 253, 0.1) !important;
          }

          &::placeholder {
            color: #999999 !important;
            font-style: italic;
          }
        }

        .el-input__count {
          color: #999999 !important;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    background: #ffffff !important;
    border-top: 1px solid #f0f0f0;
    text-align: right;

    .reference-btn {
      margin-right: 12px;
      padding: 10px 16px;
      border: 1px solid #dcdfe6 !important;
      border-radius: 4px;
      color: #606266 !important;
      background: #ffffff !important;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        border-color: #c0c4cc !important;
        color: #409eff !important;
        background: #ecf5ff !important;
      }
    }

    .cancel-btn {
      margin-right: 12px;
      padding: 10px 20px;
      border: 1px solid #dcdfe6 !important;
      border-radius: 4px;
      color: #606266 !important;
      background: #ffffff !important;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        border-color: #c0c4cc !important;
        color: #409eff !important;
        background: #ecf5ff !important;
      }
    }

    .confirm-btn {
      padding: 10px 24px;
      border-radius: 6px;
      background: #4ea0fd !important;
      border: 1px solid #4ea0fd !important;
      color: #ffffff !important;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(78, 160, 253, 0.3);
      transition: all 0.2s ease;

      &:hover {
        background: #409eff !important;
        border-color: #409eff !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}




</style>
